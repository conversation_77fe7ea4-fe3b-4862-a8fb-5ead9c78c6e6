{"GlobalPropertiesHash": "2ilJ2M8+ZdH0swl4cXFj9Ji8kay0R08ISE/fEc+OL0o=", "FingerprintPatternsHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "PropertyOverridesHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "InputHashes": ["wZuFOlHsrqR9cXu6mxekkBsiC6QU3zcstBnrYDk9rks=", "CQgKH8W8qPNv/zTGi+yT4devd2fGeZtkZIsR0eOCV2Q=", "W9Bc2OxY7R7pmQeN2PQtF1WZB+9ViczX8pg76LDdXU0=", "yoXv0TI7crRvi0WSUm3PwkHgeHrRXs+kCnae4TmbeVI=", "/JhB1NJ+iFQmlTbHZOuOkVaUUP8BaHKwj6MmBlFU5LU=", "YReH+ZivAAQkSfuX5N45fi5heYMMFkEFc6gJODTYbeM=", "5DN7pKeRMC0kSKIhahtYkPXpkUxITTiZGWDZb5cqV2k=", "NOBn9KHaC0NYvI1HAFsWvSa8Kc1gKETODKnzKGPduJk=", "vxtkJEE3yl6+Ay3DcdGsNvnLGAefFIyNLgTtHgEB/oE=", "D231o3mCYGo7WFC9rYsZbfL0xfhEUZYLzNl3uFuKBrw=", "LIJYu0i0YJjlRsX5F7s/h9Rcs34/+17slEPTkHxka3Q=", "vbC0bxxK9UlVRkkGZmSjBFbLqARNQqQ0Kjx3UumpGW0=", "wAH9xD7DFBQNc5psE4hFIBh2WLqQibiekgO8gfh0nC0=", "cn6xG505yeSncxYTv68s7iv+KM1NhMBFFotwBbZ0jOU=", "DZWGeJTfA0bh03vIsuKgQ2/32xTX1H9XtBl8lOmLUmk=", "l/1liMfFjS50oJ4iR0we4L/9D1CaRSgxOoe7V9eSRts=", "Yh+o0qp0zdQkZrkgq6VUrebLUHvEIFAOBNZV4Cgesuo=", "Hlkcao6EwbTIqDkVD9JzqMnNjSQQ31DkWD0ZnAGtamY=", "wipPKTmy29H37uC5Be8M7xpKZnCNSwmeFgMk0L7VgCM=", "j8ja4JIMamcs+p5Y4pCsNQOlag6NR9QUrCj+QN2lZdw=", "bFwl2rDqR/kv3dJ81bZvvyhDc4MExUnQRFLMRWMIg2A=", "5PV+Gac/azE1r9amR+M/kwtefuff9J64z8bq5//Bnn4=", "w29yXDS90a+QfEX0pD5QI9vTGnyPdjKVFel0kMsCZDA=", "wMBim0P9OCdCiCu0AyAVTIlx6hDRl4R2cKrAK27bc+A=", "KrHAR/WcEB9BeJDAUNlP25TkdkYLcDpFEN2rIwCQe3A=", "cJqQCEnvkybQ0TJSczCEi/qO7oaEh4so+2ZA1ikWBX8=", "ZJJCjZGK8/K3ortPDF38ibbz1c06xgVhpmx1dqb1a7o=", "5tEDZGph8OZi5r/5dZeQQ3VUy4m+RG8hCEDfRNPtB3w=", "1F0A37YHD9aeh6U6UN7fJAqeHdBTqL55gsBBWSkAjGA=", "WQosWx6BCh2+pMawb/eQq/jKTDxq1UrO/yZNjw8onck=", "VmwtgzXWGOuPBdMaeTNMN8NhV1dP786tX7ZxKCdTEsI=", "bNpsDzBhKijy9VBZE0cRSjbvjP4Rlcpi4fq77isz+mE=", "pmlqZZZicHDGXE1FMXrat1td5gNjBN33Ub4qz6JvPIM=", "+jGaWq1SdVQ8qKCyzKMzUMX44MocByeJIUwmh3eihrg=", "z5lxMBbq39SkGGA+DfAOigvCx/ExDZFS1qxp0pZ4nRY="], "CachedAssets": {"wZuFOlHsrqR9cXu6mxekkBsiC6QU3zcstBnrYDk9rks=": {"Identity": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\uucirysn9r-hdhjllpya4.gz", "SourceId": "Saml2AuthDemo", "SourceType": "Discovered", "ContentRoot": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Saml2AuthDemo", "RelativePath": "css/site#[.{fingerprint=hdhjllpya4}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\css\\site.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "0tevtyurkr", "Integrity": "TP7j5uZuJdntcOfcko7umOSdXFMdhRUKfjFd0AWnaFI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\css\\site.css", "FileLength": 641, "LastWriteTime": "2025-05-29T06:01:30+00:00"}, "CQgKH8W8qPNv/zTGi+yT4devd2fGeZtkZIsR0eOCV2Q=": {"Identity": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\0c2qurj59r-90yqlj465b.gz", "SourceId": "Saml2AuthDemo", "SourceType": "Discovered", "ContentRoot": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Saml2AuthDemo", "RelativePath": "favicon#[.{fingerprint=90yqlj465b}]?.ico.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\favicon.ico", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "xngppdbdhq", "Integrity": "nR0lNhVJOqtsVYCBNYXOoTLPTwP88AJKUlf4evu9xkE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\favicon.ico", "FileLength": 9431, "LastWriteTime": "2025-05-29T06:01:30+00:00"}, "W9Bc2OxY7R7pmQeN2PQtF1WZB+9ViczX8pg76LDdXU0=": {"Identity": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\llfevw3361-0n9f8i66x6.gz", "SourceId": "Saml2AuthDemo", "SourceType": "Discovered", "ContentRoot": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Saml2AuthDemo", "RelativePath": "js/site#[.{fingerprint=0n9f8i66x6}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\js\\site.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "kimwdh415r", "Integrity": "/Mp0M0f41yhPFkzdAUe9swvvjRlvmhMty1mZZSRdmG0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\js\\site.js", "FileLength": 186, "LastWriteTime": "2025-05-29T06:01:30+00:00"}, "yoXv0TI7crRvi0WSUm3PwkHgeHrRXs+kCnae4TmbeVI=": {"Identity": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\dunpfset9o-9n0ta5ieki.gz", "SourceId": "Saml2AuthDemo", "SourceType": "Discovered", "ContentRoot": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Saml2AuthDemo", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid#[.{fingerprint=9n0ta5ieki}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "xhmut9o5n8", "Integrity": "ZY60kbNkADk4KDRhW+LL+izegJBs9qdfvXorvDd7lIc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "FileLength": 6966, "LastWriteTime": "2025-05-29T06:01:30+00:00"}, "/JhB1NJ+iFQmlTbHZOuOkVaUUP8BaHKwj6MmBlFU5LU=": {"Identity": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\6lzqm95o0o-0i1dcxd824.gz", "SourceId": "Saml2AuthDemo", "SourceType": "Discovered", "ContentRoot": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Saml2AuthDemo", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.css#[.{fingerprint=0i1dcxd824}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ligbo5mwl1", "Integrity": "oytvk6SQ3cnlPxAF33gxI7GS06Rj2MrRoUPoiaFchjQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "FileLength": 27062, "LastWriteTime": "2025-05-29T06:01:30+00:00"}, "YReH+ZivAAQkSfuX5N45fi5heYMMFkEFc6gJODTYbeM=": {"Identity": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\gcqjvrmgyi-vxs71z90fw.gz", "SourceId": "Saml2AuthDemo", "SourceType": "Discovered", "ContentRoot": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Saml2AuthDemo", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min#[.{fingerprint=vxs71z90fw}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "otvwzz8k95", "Integrity": "HFjfcB/gV8FeYhFO87HnfGGK/Ebdg885i044QcdVncY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "FileLength": 6029, "LastWriteTime": "2025-05-29T06:01:30+00:00"}, "5DN7pKeRMC0kSKIhahtYkPXpkUxITTiZGWDZb5cqV2k=": {"Identity": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\ae6gh6t3i1-jzb7jyrjvs.gz", "SourceId": "Saml2AuthDemo", "SourceType": "Discovered", "ContentRoot": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Saml2AuthDemo", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min.css#[.{fingerprint=jzb7jyrjvs}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "iez2ehrrlx", "Integrity": "ukFugDNptsdF2I0tTFVp+zrq3vCFvErB7PNxY2Pmf7s=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "FileLength": 13935, "LastWriteTime": "2025-05-29T06:01:30+00:00"}, "NOBn9KHaC0NYvI1HAFsWvSa8Kc1gKETODKnzKGPduJk=": {"Identity": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\0dvwnkaaa0-nynt4yc5xr.gz", "SourceId": "Saml2AuthDemo", "SourceType": "Discovered", "ContentRoot": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Saml2AuthDemo", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot#[.{fingerprint=nynt4yc5xr}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "yr6g57tbrs", "Integrity": "ski9h0PufafI6hMaaPSRPSY+iMBc1VbPoK1FYjUXHMQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "FileLength": 1737, "LastWriteTime": "2025-05-29T06:01:30+00:00"}, "vxtkJEE3yl6+Ay3DcdGsNvnLGAefFIyNLgTtHgEB/oE=": {"Identity": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\ye5ch5e0ju-tgg2bl5mrw.gz", "SourceId": "Saml2AuthDemo", "SourceType": "Discovered", "ContentRoot": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Saml2AuthDemo", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.css#[.{fingerprint=tgg2bl5mrw}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2shzy9x2jj", "Integrity": "PgWj9lwXprPCKRJlELXHPizyFeevIvOzAMOrcCGtBdI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "FileLength": 17007, "LastWriteTime": "2025-05-29T06:01:30+00:00"}, "D231o3mCYGo7WFC9rYsZbfL0xfhEUZYLzNl3uFuKBrw=": {"Identity": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\odolngyg4q-brwg1hntyu.gz", "SourceId": "Saml2AuthDemo", "SourceType": "Discovered", "ContentRoot": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Saml2AuthDemo", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min#[.{fingerprint=brwg1hntyu}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "450chlim9d", "Integrity": "SK/uC88mzOjgHwihIpIpaGtZcvPkQkQKkO0iusPs+Z0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "FileLength": 1615, "LastWriteTime": "2025-05-29T06:01:30+00:00"}, "LIJYu0i0YJjlRsX5F7s/h9Rcs34/+17slEPTkHxka3Q=": {"Identity": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\s7wjcvroec-gf2dxac9qe.gz", "SourceId": "Saml2AuthDemo", "SourceType": "Discovered", "ContentRoot": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Saml2AuthDemo", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min.css#[.{fingerprint=gf2dxac9qe}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "m8laq02kqt", "Integrity": "+vu0DGLWqkGPTrKaf7U22OpO4XImAF0LW8HvFHEnCKA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "FileLength": 8039, "LastWriteTime": "2025-05-29T06:01:30+00:00"}, "vbC0bxxK9UlVRkkGZmSjBFbLqARNQqQ0Kjx3UumpGW0=": {"Identity": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\xl2utgi5t6-gawgt6fljy.gz", "SourceId": "Saml2AuthDemo", "SourceType": "Discovered", "ContentRoot": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Saml2AuthDemo", "RelativePath": "lib/bootstrap/dist/css/bootstrap#[.{fingerprint=gawgt6fljy}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "20hnowj6dn", "Integrity": "LgKX0350Ug6XsDByPQMOQx1sihJrlzqNLL47LiyoSQ4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "FileLength": 25746, "LastWriteTime": "2025-05-29T06:01:30+00:00"}, "wAH9xD7DFBQNc5psE4hFIBh2WLqQibiekgO8gfh0nC0=": {"Identity": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\kwpabttjim-k9n1kkbua6.gz", "SourceId": "Saml2AuthDemo", "SourceType": "Discovered", "ContentRoot": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Saml2AuthDemo", "RelativePath": "lib/bootstrap/dist/css/bootstrap.css#[.{fingerprint=k9n1kkbua6}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "8nia3m3p3a", "Integrity": "HBgFcfSZJMq1nxRljAc5eNlMAVuXqU0lQoR9rBIy968=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "FileLength": 98033, "LastWriteTime": "2025-05-29T06:01:30+00:00"}, "cn6xG505yeSncxYTv68s7iv+KM1NhMBFFotwBbZ0jOU=": {"Identity": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\0kpipdbxfq-etnb7xlipe.gz", "SourceId": "Saml2AuthDemo", "SourceType": "Discovered", "ContentRoot": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Saml2AuthDemo", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min#[.{fingerprint=etnb7xlipe}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "fg8y3a0rbt", "Integrity": "T2rPGX2rORFBp2vX1wlC4q43KPqCjKbVkY+VHIDgmP0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "FileLength": 23242, "LastWriteTime": "2025-05-29T06:01:30+00:00"}, "DZWGeJTfA0bh03vIsuKgQ2/32xTX1H9XtBl8lOmLUmk=": {"Identity": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\3l64p4di12-kao5znno1s.gz", "SourceId": "Saml2AuthDemo", "SourceType": "Discovered", "ContentRoot": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Saml2AuthDemo", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min.css#[.{fingerprint=kao5znno1s}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "py1k7jl89u", "Integrity": "8SkbHnWWeJ8KG1+bQUkOaPN31v/xtm4ZycUWB/SvS20=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "FileLength": 102069, "LastWriteTime": "2025-05-29T06:01:30+00:00"}, "l/1liMfFjS50oJ4iR0we4L/9D1CaRSgxOoe7V9eSRts=": {"Identity": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\cbjq4inumo-u0biprgly9.gz", "SourceId": "Saml2AuthDemo", "SourceType": "Discovered", "ContentRoot": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Saml2AuthDemo", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle#[.{fingerprint=u0biprgly9}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hy9fihjyo6", "Integrity": "KcZfC8VlRGpzBSf8kYjbjjvxZ1mENKljqzW5X1VIqCY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "FileLength": 48345, "LastWriteTime": "2025-05-29T06:01:30+00:00"}, "Yh+o0qp0zdQkZrkgq6VUrebLUHvEIFAOBNZV4Cgesuo=": {"Identity": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\cip0knvmfd-pxamm17y9e.gz", "SourceId": "Saml2AuthDemo", "SourceType": "Discovered", "ContentRoot": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Saml2AuthDemo", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.js#[.{fingerprint=pxamm17y9e}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "99x6gwa75b", "Integrity": "IyKBFJ2NdYfuuICMUQxeCcGk/bTVfEdW5iAvM9nw0oU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "FileLength": 91460, "LastWriteTime": "2025-05-29T06:01:30+00:00"}, "Hlkcao6EwbTIqDkVD9JzqMnNjSQQ31DkWD0ZnAGtamY=": {"Identity": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\zo36bj6qtt-hjuzisly30.gz", "SourceId": "Saml2AuthDemo", "SourceType": "Discovered", "ContentRoot": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Saml2AuthDemo", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min#[.{fingerprint=hjuzisly30}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "0nrdyyg5oh", "Integrity": "1MiwfybWPUf85W8WHsUjrjh6SWZrvKae/f829+FAkhU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "FileLength": 22298, "LastWriteTime": "2025-05-29T06:01:30+00:00"}, "wipPKTmy29H37uC5Be8M7xpKZnCNSwmeFgMk0L7VgCM=": {"Identity": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\ba2jr6qd4r-u9xms436mi.gz", "SourceId": "Saml2AuthDemo", "SourceType": "Discovered", "ContentRoot": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Saml2AuthDemo", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min.js#[.{fingerprint=u9xms436mi}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ka41v8yo7c", "Integrity": "BHUyrJ6l5FX9FTIykDazdjlP73jzpfplry3r+6zSdoE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "FileLength": 82041, "LastWriteTime": "2025-05-29T06:01:30+00:00"}, "j8ja4JIMamcs+p5Y4pCsNQOlag6NR9QUrCj+QN2lZdw=": {"Identity": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\s7f8b8w26r-48vr37mrsy.gz", "SourceId": "Saml2AuthDemo", "SourceType": "Discovered", "ContentRoot": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Saml2AuthDemo", "RelativePath": "lib/bootstrap/dist/js/bootstrap#[.{fingerprint=48vr37mrsy}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "gd33sbxxto", "Integrity": "1Ct5pGkD+FF0LlXrT4xzvGbCR79KcfwrOutx4z65BIc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "FileLength": 25283, "LastWriteTime": "2025-05-29T06:01:30+00:00"}, "bFwl2rDqR/kv3dJ81bZvvyhDc4MExUnQRFLMRWMIg2A=": {"Identity": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\lyme58ep3m-vaswmcjbo4.gz", "SourceId": "Saml2AuthDemo", "SourceType": "Discovered", "ContentRoot": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Saml2AuthDemo", "RelativePath": "lib/bootstrap/dist/js/bootstrap.js#[.{fingerprint=vaswmcjbo4}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "t7dzx6h33q", "Integrity": "V2nzFYIGfzW4T0n5xREU4Xq27AL4FR+ObKGcPUp3R8I=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "FileLength": 57786, "LastWriteTime": "2025-05-29T06:01:30+00:00"}, "5PV+Gac/azE1r9amR+M/kwtefuff9J64z8bq5//Bnn4=": {"Identity": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\448jultfmw-zu238p5lxg.gz", "SourceId": "Saml2AuthDemo", "SourceType": "Discovered", "ContentRoot": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Saml2AuthDemo", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min#[.{fingerprint=zu238p5lxg}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "b9b8zdnevc", "Integrity": "/zGCumvrl91atAvEbBmyY+KKn65ToLyg+icn9FQP8xU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "FileLength": 15441, "LastWriteTime": "2025-05-29T06:01:30+00:00"}, "w29yXDS90a+QfEX0pD5QI9vTGnyPdjKVFel0kMsCZDA=": {"Identity": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\9hizxlbrr4-sgi57dik4g.gz", "SourceId": "Saml2AuthDemo", "SourceType": "Discovered", "ContentRoot": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Saml2AuthDemo", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min.js#[.{fingerprint=sgi57dik4g}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "l0a66zixp4", "Integrity": "y/W+IS3pBW6YOxougP7451HTyQ/Q1y9J/D94i5Ds7u8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "FileLength": 46112, "LastWriteTime": "2025-05-29T06:01:30+00:00"}, "wMBim0P9OCdCiCu0AyAVTIlx6hDRl4R2cKrAK27bc+A=": {"Identity": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\bt4acyjzvx-dvc2bfcndg.gz", "SourceId": "Saml2AuthDemo", "SourceType": "Discovered", "ContentRoot": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Saml2AuthDemo", "RelativePath": "lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive#[.{fingerprint=dvc2bfcndg}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\jquery-validation-unobtrusive\\jquery.validate.unobtrusive.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ia1fp3fsqk", "Integrity": "tra5GpUITUrALtnm990ZmXUgYSqsrYHJ2wct2dSXNwo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\jquery-validation-unobtrusive\\jquery.validate.unobtrusive.js", "FileLength": 4685, "LastWriteTime": "2025-05-29T06:01:30+00:00"}, "KrHAR/WcEB9BeJDAUNlP25TkdkYLcDpFEN2rIwCQe3A=": {"Identity": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\xd5r9ktl5s-gb7ocvbhts.gz", "SourceId": "Saml2AuthDemo", "SourceType": "Discovered", "ContentRoot": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Saml2AuthDemo", "RelativePath": "lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.min#[.{fingerprint=gb7ocvbhts}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\jquery-validation-unobtrusive\\jquery.validate.unobtrusive.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "y5r6ekb0nn", "Integrity": "+uJmiWNDfG3y+8SUapZ3/8iFhrZEO4UoNTds6PJqncM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\jquery-validation-unobtrusive\\jquery.validate.unobtrusive.min.js", "FileLength": 2220, "LastWriteTime": "2025-05-29T06:01:30+00:00"}, "cJqQCEnvkybQ0TJSczCEi/qO7oaEh4so+2ZA1ikWBX8=": {"Identity": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\92bk6dxr5j-wqejeusuyq.gz", "SourceId": "Saml2AuthDemo", "SourceType": "Discovered", "ContentRoot": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Saml2AuthDemo", "RelativePath": "lib/jquery-validation-unobtrusive/LICENSE#[.{fingerprint=wqejeusuyq}]?.txt.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qmy2g5n6hh", "Integrity": "0k+GY7B37PlcFstK7JIg2ptnyI+BsfB92+jtjLIJOuo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "FileLength": 395, "LastWriteTime": "2025-05-29T06:01:30+00:00"}, "ZJJCjZGK8/K3ortPDF38ibbz1c06xgVhpmx1dqb1a7o=": {"Identity": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\5ikut97hnh-wus95c49fh.gz", "SourceId": "Saml2AuthDemo", "SourceType": "Discovered", "ContentRoot": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Saml2AuthDemo", "RelativePath": "lib/jquery-validation/dist/additional-methods#[.{fingerprint=wus95c49fh}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qur712kw8c", "Integrity": "Pk7e9oX4/+O7Ztv+d0Yk/GdqClCzkF9l6oJgJLD6RgU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "FileLength": 11698, "LastWriteTime": "2025-05-29T06:01:30+00:00"}, "5tEDZGph8OZi5r/5dZeQQ3VUy4m+RG8hCEDfRNPtB3w=": {"Identity": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\dvdvw0odmf-sjab29p8z5.gz", "SourceId": "Saml2AuthDemo", "SourceType": "Discovered", "ContentRoot": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Saml2AuthDemo", "RelativePath": "lib/jquery-validation/dist/additional-methods.min#[.{fingerprint=sjab29p8z5}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "w2h719sd9u", "Integrity": "OLeOSYXppYtjegTqvAF1VW6jF7Rk/TBkhxwj44Fhlbc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "FileLength": 5460, "LastWriteTime": "2025-05-29T06:01:30+00:00"}, "1F0A37YHD9aeh6U6UN7fJAqeHdBTqL55gsBBWSkAjGA=": {"Identity": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\cs0x98z2f0-d2pxujwhw3.gz", "SourceId": "Saml2AuthDemo", "SourceType": "Discovered", "ContentRoot": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Saml2AuthDemo", "RelativePath": "lib/jquery-validation/dist/jquery.validate#[.{fingerprint=d2pxujwhw3}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "scqjpphbl3", "Integrity": "LJlWNqc+gAs3U5I+jF4VyB+HKQmiECp60pjSAw/gqhs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "FileLength": 13101, "LastWriteTime": "2025-05-29T06:01:30+00:00"}, "WQosWx6BCh2+pMawb/eQq/jKTDxq1UrO/yZNjw8onck=": {"Identity": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\ovqzhavyxs-4dm14o4hmc.gz", "SourceId": "Saml2AuthDemo", "SourceType": "Discovered", "ContentRoot": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Saml2AuthDemo", "RelativePath": "lib/jquery-validation/dist/jquery.validate.min#[.{fingerprint=4dm14o4hmc}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "41f15l79m5", "Integrity": "WEtE86OkwYRC2CNB1ewkbZ+Kqi1+CF3HTu2hd7e2V+k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "FileLength": 7505, "LastWriteTime": "2025-05-29T06:01:30+00:00"}, "VmwtgzXWGOuPBdMaeTNMN8NhV1dP786tX7ZxKCdTEsI=": {"Identity": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\7981pj8p43-x0q3zqp4vz.gz", "SourceId": "Saml2AuthDemo", "SourceType": "Discovered", "ContentRoot": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Saml2AuthDemo", "RelativePath": "lib/jquery-validation/LICENSE#[.{fingerprint=x0q3zqp4vz}]?.md.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\jquery-validation\\LICENSE.md", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "tbedkqp182", "Integrity": "yZU1lf/p2dEnCHUXYp8G4roAyod23x5MoR4DehwHfrA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\jquery-validation\\LICENSE.md", "FileLength": 670, "LastWriteTime": "2025-05-29T06:01:30+00:00"}, "bNpsDzBhKijy9VBZE0cRSjbvjP4Rlcpi4fq77isz+mE=": {"Identity": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\k6ix4t2cun-a8eee2794i.gz", "SourceId": "Saml2AuthDemo", "SourceType": "Discovered", "ContentRoot": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Saml2AuthDemo", "RelativePath": "lib/jquery/dist/jquery#[.{fingerprint=a8eee2794i}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\jquery\\dist\\jquery.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "x92lfpy4r7", "Integrity": "xZFJpzAOm0oPUAPPaOaOm9PjEktxu2/+op37a3inY8Y=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\jquery\\dist\\jquery.js", "FileLength": 81613, "LastWriteTime": "2025-05-29T06:01:30+00:00"}, "pmlqZZZicHDGXE1FMXrat1td5gNjBN33Ub4qz6JvPIM=": {"Identity": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\9vs9glwrv0-fc9074g7ds.gz", "SourceId": "Saml2AuthDemo", "SourceType": "Discovered", "ContentRoot": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Saml2AuthDemo", "RelativePath": "lib/jquery/dist/jquery.min#[.{fingerprint=fc9074g7ds}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\jquery\\dist\\jquery.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "j3ly93bv7m", "Integrity": "T6QtgY4DAKUwGXkADndGXihd1P1yhbRrSJEJmozFCKQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\jquery\\dist\\jquery.min.js", "FileLength": 30358, "LastWriteTime": "2025-05-29T06:01:30+00:00"}, "+jGaWq1SdVQ8qKCyzKMzUMX44MocByeJIUwmh3eihrg=": {"Identity": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\7iaxxtbiqn-b804xvqo1w.gz", "SourceId": "Saml2AuthDemo", "SourceType": "Discovered", "ContentRoot": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Saml2AuthDemo", "RelativePath": "lib/jquery/dist/jquery.min#[.{fingerprint=b804xvqo1w}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\jquery\\dist\\jquery.min.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4nebbfrnps", "Integrity": "4BWp/edO0rIyA+BxiKPHBnzmhoRLav1dEQiSlY7ixcc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\jquery\\dist\\jquery.min.map", "FileLength": 53014, "LastWriteTime": "2025-05-29T06:01:30+00:00"}, "z5lxMBbq39SkGGA+DfAOigvCx/ExDZFS1qxp0pZ4nRY=": {"Identity": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\ey517g5lte-afgyafcsqt.gz", "SourceId": "Saml2AuthDemo", "SourceType": "Discovered", "ContentRoot": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Saml2AuthDemo", "RelativePath": "lib/jquery/LICENSE#[.{fingerprint=afgyafcsqt}]?.txt.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\jquery\\LICENSE.txt", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "em4b14lj27", "Integrity": "2tL6ssgUvPLUDr8vBWmWHtk+7IqutyuHFEGAG7jexBw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\jquery\\LICENSE.txt", "FileLength": 933, "LastWriteTime": "2025-05-29T06:01:30+00:00"}}, "CachedCopyCandidates": {}}