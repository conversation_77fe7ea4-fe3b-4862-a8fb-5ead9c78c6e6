@echo off
setlocal enabledelayedexpansion

echo Starting DUO SAML Demo Development Environment...

REM Check prerequisites
echo Checking prerequisites...

where dotnet >nul 2>nul
if %errorlevel% neq 0 (
    echo Error: .NET is not installed or not in PATH
    pause
    exit /b 1
)

where node >nul 2>nul
if %errorlevel% neq 0 (
    echo Error: Node.js is not installed or not in PATH
    pause
    exit /b 1
)

where npm >nul 2>nul
if %errorlevel% neq 0 (
    echo Error: npm is not installed or not in PATH
    pause
    exit /b 1
)

echo Prerequisites check passed!

REM Start the ASP.NET Core Web API backend
echo Starting ASP.NET Core Web API backend...
cd Saml2AuthDemo.WebApi
start "Backend API" cmd /k "dotnet run --urls=http://localhost:5001"
cd ..

REM Wait for backend to start
echo Waiting for backend to start...
timeout /t 5 /nobreak >nul

REM Install React dependencies if needed
if not exist "react-frontend\node_modules" (
    echo Installing React dependencies...
    cd react-frontend
    call npm install
    cd ..
)

REM Start the React frontend
echo Starting React frontend...
cd react-frontend
start "React Frontend" cmd /k "npm start"
cd ..

echo.
echo Development environment started successfully!
echo Backend API: http://localhost:5001
echo Frontend: http://localhost:3000
echo API Documentation: http://localhost:5001/swagger
echo.
echo Both applications are running in separate windows.
echo Close the command windows to stop the applications.
echo.
pause
