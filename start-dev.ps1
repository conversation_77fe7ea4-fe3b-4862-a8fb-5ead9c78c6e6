# PowerShell script to start both backend and frontend for development

Write-Host "Starting DUO SAML Demo Development Environment..." -ForegroundColor Green

# Start the ASP.NET Core backend
Write-Host "Starting ASP.NET Core backend..." -ForegroundColor Yellow
Start-Process powershell -ArgumentList "-NoExit", "-Command", "cd 'Saml2AuthDemo'; dotnet run"

# Wait a moment for the backend to start
Start-Sleep -Seconds 3

# Start the React frontend
Write-Host "Starting React frontend..." -ForegroundColor Yellow
Start-Process powershell -ArgumentList "-NoExit", "-Command", "cd 'react-frontend'; npm start"

Write-Host "Development environment started!" -ForegroundColor Green
Write-Host "Backend: https://localhost:5001" -ForegroundColor Cyan
Write-Host "Frontend: http://localhost:3000" -ForegroundColor Cyan
Write-Host "Press any key to exit..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
