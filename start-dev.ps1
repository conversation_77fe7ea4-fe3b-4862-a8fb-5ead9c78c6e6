# PowerShell script to start both backend and frontend for development

Write-Host "Starting DUO SAML Demo Development Environment..." -ForegroundColor Green

# Check prerequisites
Write-Host "Checking prerequisites..." -ForegroundColor Yellow

if (!(Get-Command dotnet -ErrorAction SilentlyContinue)) {
    Write-Host "Error: .NET is not installed or not in PATH" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

if (!(Get-Command node -ErrorAction SilentlyContinue)) {
    Write-Host "Error: Node.js is not installed or not in PATH" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

if (!(Get-Command npm -ErrorAction SilentlyContinue)) {
    Write-Host "Error: npm is not installed or not in PATH" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host "Prerequisites check passed!" -ForegroundColor Green

# Start the ASP.NET Core Web API backend
Write-Host "Starting ASP.NET Core Web API backend..." -ForegroundColor Yellow
Start-Process powershell -ArgumentList "-NoExit", "-Command", "cd 'Saml2AuthDemo.WebApi'; dotnet run --urls='http://localhost:5001'"

# Wait a moment for the backend to start
Start-Sleep -Seconds 5

# Install React dependencies if needed
if (!(Test-Path "react-frontend\node_modules")) {
    Write-Host "Installing React dependencies..." -ForegroundColor Yellow
    Set-Location "react-frontend"
    npm install
    Set-Location ".."
}

# Start the React frontend
Write-Host "Starting React frontend..." -ForegroundColor Yellow
Start-Process powershell -ArgumentList "-NoExit", "-Command", "cd 'react-frontend'; npm start"

Write-Host "Development environment started!" -ForegroundColor Green
Write-Host "Backend API: http://localhost:5001" -ForegroundColor Cyan
Write-Host "Frontend: http://localhost:3000" -ForegroundColor Cyan
Write-Host "API Documentation: http://localhost:5001/swagger" -ForegroundColor Cyan
Write-Host ""
Write-Host "Both applications are running in separate windows." -ForegroundColor Gray
Write-Host "Close the PowerShell windows to stop the applications." -ForegroundColor Gray
Write-Host "Press any key to exit this script..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
