import React from 'react';
import { useAuth } from '../context/AuthContext';

const Home = () => {
  const { isAuthenticated, simulateAuthentication } = useAuth();

  return (
    <div className="text-center">
      <h1 className="display-4">DUO SAML Demo</h1>
      {!isAuthenticated && (
        <div className="mt-4">
          <p className="lead">Welcome to the DUO SAML Demo application.</p>
          <p>Click "Login" in the navigation to authenticate via SAML.</p>
          <div className="mt-3">
            <button
              className="btn btn-secondary"
              onClick={simulateAuthentication}
            >
              Demo Mode (Simulate Authentication)
            </button>
            <small className="d-block mt-2 text-muted">
              For testing purposes only - simulates a successful SAML authentication
            </small>
          </div>
        </div>
      )}
      {isAuthenticated && (
        <div className="mt-4">
          <p className="lead">You are successfully authenticated!</p>
          <p>Visit the <a href="/claims">SAML Claims</a> page to view your user claims.</p>
        </div>
      )}
    </div>
  );
};

export default Home;
