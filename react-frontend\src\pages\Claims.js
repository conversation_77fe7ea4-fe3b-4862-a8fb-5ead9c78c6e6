import React from 'react';
import { useAuth } from '../context/AuthContext';

const Claims = () => {
  const { claims, isAuthenticated } = useAuth();

  if (!isAuthenticated) {
    return (
      <div className="row">
        <div className="col-md-12">
          <h2>Access Denied</h2>
          <p>You must be logged in to view this page.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="row">
      <div className="col-md-12">
        <h2>The users Claims (Iteration on User.Claims)</h2>
        <p>
          {claims && claims.length > 0 ? (
            claims.map((claim, index) => (
              <span key={index}>
                <strong>{claim.type}</strong> <br /> 
                <span style={{ paddingLeft: '10px' }}>Value: {claim.value}</span> <br />
              </span>
            ))
          ) : (
            <span>No claims available.</span>
          )}
        </p>
      </div>
    </div>
  );
};

export default Claims;
