using System.Security.Claims;
using ITfoxtec.Identity.Saml2.Claims;

namespace Saml2AuthDemo.WebApi.Utils;

public static class ClaimsTransform
{
    public static ClaimsPrincipal Transform(ClaimsPrincipal incomingPrincipal)
    {
        return !incomingPrincipal.Identity!.IsAuthenticated ? incomingPrincipal : CreateClaimsPrincipal(incomingPrincipal);
    }

    private static ClaimsPrincipal CreateClaimsPrincipal(ClaimsPrincipal incomingPrincipal)
    {
        var claims = new List<Claim>();

        claims.AddRange(incomingPrincipal.Claims);

        return new ClaimsPrincipal(new ClaimsIdentity(claims, incomingPrincipal.Identity!.AuthenticationType, ClaimTypes.NameIdentifier, ClaimTypes.Role)
        {
            BootstrapContext = ((ClaimsIdentity)incomingPrincipal.Identity).BootstrapContext
        });
    }

    private static IEnumerable<Claim> GetSaml2LogoutClaims(ClaimsPrincipal principal)
    {
        yield return GetClaim(principal, Saml2ClaimTypes.NameId);
        yield return GetClaim(principal, Saml2ClaimTypes.NameIdFormat);
        yield return GetClaim(principal, Saml2ClaimTypes.SessionIndex);
    }

    private static Claim GetClaim(ClaimsPrincipal principal, string claimType)
    {
        return ((ClaimsIdentity)principal.Identity).Claims.FirstOrDefault(c => c.Type == claimType);
    }

    private static string GetClaimValue(ClaimsPrincipal principal, string claimType)
    {
        var claim = GetClaim(principal, claimType);
        return claim.Value;
    }
}
