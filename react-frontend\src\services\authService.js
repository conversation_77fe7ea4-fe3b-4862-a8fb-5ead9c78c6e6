// Authentication service to handle SAML authentication
class AuthService {
  async checkAuthStatus() {
    try {
      // In a real implementation, this would call your backend API
      // For now, we'll simulate checking authentication status
      const response = await fetch('/api/auth/status', {
        method: 'GET',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const data = await response.json();
        return {
          isAuthenticated: data.isAuthenticated,
          user: data.user,
          claims: data.claims
        };
      }
      
      return { isAuthenticated: false, user: null, claims: [] };
    } catch (error) {
      console.error('Error checking auth status:', error);
      return { isAuthenticated: false, user: null, claims: [] };
    }
  }

  async logout() {
    try {
      const response = await fetch('/Auth/Logout', {
        method: 'POST',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
      });

      if (!response.ok) {
        throw new Error('Logout failed');
      }

      return true;
    } catch (error) {
      console.error('Logout error:', error);
      throw error;
    }
  }

  // Mock data for development/demo purposes
  getMockAuthData() {
    return {
      isAuthenticated: true,
      user: {
        name: 'John Doe',
        email: '<EMAIL>'
      },
      claims: [
        { type: 'http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier', value: '<EMAIL>' },
        { type: 'http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name', value: 'John Doe' },
        { type: 'http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress', value: '<EMAIL>' },
        { type: 'http://schemas.microsoft.com/ws/2008/06/identity/claims/role', value: 'User' },
        { type: 'http://schemas.xmlsoap.org/ws/2005/05/identity/claims/givenname', value: 'John' },
        { type: 'http://schemas.xmlsoap.org/ws/2005/05/identity/claims/surname', value: 'Doe' }
      ]
    };
  }
}

export const authService = new AuthService();
