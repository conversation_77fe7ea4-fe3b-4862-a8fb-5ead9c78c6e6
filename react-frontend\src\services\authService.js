// Authentication service to handle SAML authentication
class AuthService {
  constructor() {
    this.baseUrl = process.env.REACT_APP_API_URL || 'http://localhost:5001';
  }

  async checkAuthStatus() {
    try {
      const response = await fetch(`${this.baseUrl}/api/auth/status`, {
        method: 'GET',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const data = await response.json();
        return {
          isAuthenticated: data.isAuthenticated,
          user: data.user,
          claims: data.claims
        };
      }

      return { isAuthenticated: false, user: null, claims: [] };
    } catch (error) {
      console.error('Error checking auth status:', error);
      return { isAuthenticated: false, user: null, claims: [] };
    }
  }

  async logout() {
    try {
      const response = await fetch(`${this.baseUrl}/api/auth/logout`, {
        method: 'POST',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error('Logout failed');
      }

      const data = await response.json();
      return data.success;
    } catch (error) {
      console.error('Logout error:', error);
      throw error;
    }
  }

  getLoginUrl(returnUrl = null) {
    const url = new URL(`${this.baseUrl}/Auth/Login`);
    if (returnUrl) {
      url.searchParams.append('returnUrl', returnUrl);
    }
    return url.toString();
  }

  // Mock data for development/demo purposes
  getMockAuthData() {
    return {
      isAuthenticated: true,
      user: {
        name: 'John Doe',
        email: '<EMAIL>'
      },
      claims: [
        { type: 'http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier', value: '<EMAIL>' },
        { type: 'http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name', value: 'John Doe' },
        { type: 'http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress', value: '<EMAIL>' },
        { type: 'http://schemas.microsoft.com/ws/2008/06/identity/claims/role', value: 'User' },
        { type: 'http://schemas.xmlsoap.org/ws/2005/05/identity/claims/givenname', value: 'John' },
        { type: 'http://schemas.xmlsoap.org/ws/2005/05/identity/claims/surname', value: 'Doe' }
      ]
    };
  }
}

export const authService = new AuthService();
