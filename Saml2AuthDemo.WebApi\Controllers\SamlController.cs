using System.Security.Authentication;
using ITfoxtec.Identity.Saml2;
using ITfoxtec.Identity.Saml2.MvcCore;
using ITfoxtec.Identity.Saml2.Schemas;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Options;
using Saml2AuthDemo.WebApi.Utils;

namespace Saml2AuthDemo.WebApi.Controllers;

[AllowAnonymous]
[Route("Auth")]
public class SamlController(IOptions<Saml2Configuration> configAccessor) : Controller
{
    private const string RelayStateReturnUrl = "ReturnUrl";
    private readonly Saml2Configuration _config = configAccessor.Value;

    /// <summary>
    /// SAML Login endpoint - maintains original route for SAML IdP configuration
    /// </summary>
    [Route("Login")]
    public IActionResult Login(string? returnUrl = null)
    {
        var binding = new Saml2RedirectBinding();
        binding.SetRelayStateQuery(new Dictionary<string, string> 
        { 
            { RelayStateReturnUrl, returnUrl ?? "http://localhost:3000" } 
        });

        return binding.Bind(new Saml2AuthnRequest(_config)).ToActionResult();
    }

    /// <summary>
    /// SAML Assertion Consumer Service - maintains original route for SAML IdP configuration
    /// </summary>
    [Route("AssertionConsumerService")]
    public async Task<IActionResult> AssertionConsumerService()
    {
        try
        {
            var binding = new Saml2PostBinding();
            var saml2AuthnResponse = new Saml2AuthnResponse(_config);

            binding.ReadSamlResponse(Request.ToGenericHttpRequest(), saml2AuthnResponse);
            if (saml2AuthnResponse.Status != Saml2StatusCodes.Success)
            {
                throw new AuthenticationException($"SAML Response status: {saml2AuthnResponse.Status}");
            }
            
            binding.Unbind(Request.ToGenericHttpRequest(), saml2AuthnResponse);
            await saml2AuthnResponse.CreateSession(HttpContext, claimsTransform: ClaimsTransform.Transform);

            var relayStateQuery = binding.GetRelayStateQuery();
            var returnUrl = relayStateQuery.TryGetValue(RelayStateReturnUrl, out var value) ? value : "http://localhost:3000";
            
            // Redirect back to React frontend
            return Redirect(returnUrl);
        }
        catch (AuthenticationException ex)
        {
            // In a real application, you might want to redirect to an error page
            return BadRequest($"Authentication failed: {ex.Message}");
        }
        catch (Exception ex)
        {
            return StatusCode(500, $"Authentication error: {ex.Message}");
        }
    }

    /// <summary>
    /// SAML Logout endpoint - maintains original route
    /// </summary>
    [HttpPost("Logout")]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> Logout()
    {
        try
        {
            if (!User.Identity!.IsAuthenticated)
            {
                return Redirect("http://localhost:3000");
            }

            var binding = new Saml2PostBinding();
            var saml2LogoutRequest = await new Saml2LogoutRequest(_config, User).DeleteSession(HttpContext);
            
            return Redirect("http://localhost:3000");
        }
        catch (Exception)
        {
            // Even if logout fails, redirect to frontend
            return Redirect("http://localhost:3000");
        }
    }
}
