{"GlobalPropertiesHash": "bmvenSd1c1TENS/VporTJU0a5wUV9emddskNGu2BDRk=", "FingerprintPatternsHash": "gq3WsqcKBUGTSNle7RKKyXRIwh7M8ccEqOqYvIzoM04=", "PropertyOverridesHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "InputHashes": ["AyiJ+0aNT1JiVQHNSaP/PIytnxA2wuo8zIpvV9yBxus=", "yzyoj2QyO8mWHAlPIYQDi8o7/M2CCA54S2qvCkW6N20=", "xujz55Rk11zzqW6lWWHkggarPhPnRbnkOd3wGJ3XKQs=", "QQjBBdsm2NUQL4tElQbURBNu81Qf2ag8aK20yFYSmdI=", "yACfErVTsLbYmMNxhTuUz4s7wZrbThHT4eC9O7vNrRE=", "kMMfwGFlc7BEWeHA+iS39SZ6rFouVvVMzuEGPechP58=", "7NAj7WJyRQGE/SfVVVuw3mcegpCvuIGW3ore27P/4U8=", "yzDNNezH41u2eMmX8y/ArbDOSCpRJaFQCX/A5nPgGpI=", "3TGNKmwu3rNneqhaADG3QfvfEVCy9nhuK4Cb44+6Kyw=", "dfaBbfvuUZx2ozmu1pb3AhHpBWTTT/+4253t6fhtnxs=", "e1flzdptWuaKcWc3BY9x2Ks73dcoVfYsIjExKMbO5wo=", "Y75YrTAUjyq+CLbCB7gBl0Mz2A3A9eZMibdOwCaExGc=", "h98wc2zgjXbEYIWBjO2jM0yJJbelz4q2V/UhUYK5UWg=", "6qVoLQhn2JdS+bEgBAvbxwSw+xrJZHvAhFVRgaAm+xM=", "NVW6coRfWts4P4EzqGbNkIM2ASk5VakCTuK2Ug5vtD0=", "OAMpU6uibxxGLFOee+4UHSez/ekoyrCYafoM1VBHmJo=", "Y7xpcG5ZW08lcw/PPBgm5aOYLkox7ZAdhobcXyDzQpE=", "GW7snQOVhO9Nor6py1GKET4Sglj6gsV0h/G94kL1juc=", "onA1ExFey1yYg48t0bs4UItdlpKcuBgz08jcNis9lVU=", "bgHg68gG+78+Sm8N4Kou98dHa6mLrD4jrXUzK+X4a1Q=", "BjAeCksl6GOfiaTwT9hfkEb+HApywpvog1XE0Zi+9Bo=", "4TkGRgGCX/Wzs17jIanOMKffwa7da+QKuc3riUtH4Sc=", "jftsEOrAioi02BZHTjvfkGHxvOwlKXKFd60x4/g6Lzg=", "3Ku4T+DtFwp4DS4TNb0wTQIFZVjawhPAY8UdcBAGdP8=", "RHUDEcGF0X0jrjZG7iFmkSVMuUArHoQ7PxpzbaVJ0Hw=", "ZEckopBlManG4Lu5jgWhJuMz9sy0HKJWOKp+BTa+CEY=", "f0qcLhBZpELs1WIsVDSY/lTM3RQV+a4YshG7PwTJ1K0=", "T9tziKYf6vN55g5LY/Atq9mnynANn75t+F1HdgZohgg=", "0WolaGCwbTWCGcYXZbK8Q1bhdVYqrcXjs4xM6zzUD8o=", "0MXbO4AQL9OFR6QCmm+YZAtq1QyIJF3Xa+UOgUtgXYc=", "QlKTzgF+4ehUz++EQmlRq1zEvqhqotl+y05bndx9Ktc=", "VfV37M+tRNPBAk8KvEPtA7hSaWddgYTd/D6eBEN04bU=", "zQ2npQaNiekGpj/04xwH8UxilfkB/X0aiklzR2OkGRY=", "JHF/waUdJwRN+IVz1fL9f5gqbRCEJxYQTf9NglR1P3U=", "lhRgZwh2ZmzrRbkfp+SKPiRiH/Maj7V5mg7fl+WMlFA=", "caxMjb35x+pDgqE/VUBnvzCglPOefvCKmEx4caN4DqQ=", "GMBZvtYrF4ofWtR074ZbJS3jPkC9fOwPoRFWvLhu96M=", "x1cBxBqqoQYrsoDNuOZMqpSjf5Kn6MNRW6R8ulpIwFg=", "ovGowSkZonz1IFjxLbJrMSNvloRfScKywXwhcqt4BbI=", "oSKRCaeD6Vj6YyjMAshDIahqAbif99ahegGPeZROcIE=", "7K9t/hYoDd5t5fxVi2XF3vKP5ypZQ64htR2uO73QjtQ=", "W1OliBqd0v+HhhQxr8S/8aD1viDWpIR6m/G87KoPVvk=", "wIaXcOKBDPVfRTLQ+aZNx1kuLIMetn95DnWy/ow2Lx8=", "s6aiJVUjMZSdDW4DVXiyr85L8+/IlGSQeUK14f/R+WA=", "k7fylD4oIc/FoiEVBAR61M3lT0NHfW8QHh1lajg+/m4=", "QNZTuleJx9afszMCjQnLEZm8PjcbvxM6Il6vXjuiSQk="], "CachedAssets": {"AyiJ+0aNT1JiVQHNSaP/PIytnxA2wuo8zIpvV9yBxus=": {"Identity": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\css\\site.css", "SourceId": "Saml2AuthDemo", "SourceType": "Discovered", "ContentRoot": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\", "BasePath": "_content/Saml2AuthDemo", "RelativePath": "css/site#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "hdhjllpya4", "Integrity": "S2ihmzMFFc3FWmBWsR+NiddZWa8kbyaQYBx2FDkIoHs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\site.css", "FileLength": 1417, "LastWriteTime": "2025-05-14T14:28:06+00:00"}, "yzyoj2QyO8mWHAlPIYQDi8o7/M2CCA54S2qvCkW6N20=": {"Identity": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\favicon.ico", "SourceId": "Saml2AuthDemo", "SourceType": "Discovered", "ContentRoot": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\", "BasePath": "_content/Saml2AuthDemo", "RelativePath": "favicon#[.{fingerprint}]?.ico", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "90yqlj465b", "Integrity": "qU+KhVPK6oQw3UyjzAHU4xjRmCj3TLZUU/+39dni9E0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\favicon.ico", "FileLength": 32038, "LastWriteTime": "2025-05-14T14:28:06+00:00"}, "xujz55Rk11zzqW6lWWHkggarPhPnRbnkOd3wGJ3XKQs=": {"Identity": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\js\\site.js", "SourceId": "Saml2AuthDemo", "SourceType": "Discovered", "ContentRoot": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\", "BasePath": "_content/Saml2AuthDemo", "RelativePath": "js/site#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "0n9f8i66x6", "Integrity": "dLGP40S79Xnx6GqUthRF6NWvjvhQ1nOvdVSwaNcgG18=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\site.js", "FileLength": 230, "LastWriteTime": "2025-05-14T14:28:06+00:00"}, "QQjBBdsm2NUQL4tElQbURBNu81Qf2ag8aK20yFYSmdI=": {"Identity": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "SourceId": "Saml2AuthDemo", "SourceType": "Discovered", "ContentRoot": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\", "BasePath": "_content/Saml2AuthDemo", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "9n0ta5ieki", "Integrity": "ezlzqfTqJZTjEDR18neKc2tUTZtWoz3oQ/ouWY9WmO8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "FileLength": 68266, "LastWriteTime": "2025-05-14T14:28:06+00:00"}, "yACfErVTsLbYmMNxhTuUz4s7wZrbThHT4eC9O7vNrRE=": {"Identity": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "SourceId": "Saml2AuthDemo", "SourceType": "Discovered", "ContentRoot": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\", "BasePath": "_content/Saml2AuthDemo", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "0i1dcxd824", "Integrity": "2MgjO0zpqYZscatQSGWJ9Io9U8EjvXk0iYygYz1Q+Ms=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "FileLength": 151749, "LastWriteTime": "2025-05-14T14:28:06+00:00"}, "kMMfwGFlc7BEWeHA+iS39SZ6rFouVvVMzuEGPechP58=": {"Identity": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "SourceId": "Saml2AuthDemo", "SourceType": "Discovered", "ContentRoot": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\", "BasePath": "_content/Saml2AuthDemo", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "vxs71z90fw", "Integrity": "vdSFRAWr6LTognRmxyi6QlSO5O+MC+VGyMbziTrBmBQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "FileLength": 48494, "LastWriteTime": "2025-05-14T14:28:06+00:00"}, "7NAj7WJyRQGE/SfVVVuw3mcegpCvuIGW3ore27P/4U8=": {"Identity": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "SourceId": "Saml2AuthDemo", "SourceType": "Discovered", "ContentRoot": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\", "BasePath": "_content/Saml2AuthDemo", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "jzb7jyrjvs", "Integrity": "kZzlXLpTC0WvL0AL7nXf07BJ28WnY/1H/HrKgS68Q4I=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "FileLength": 108539, "LastWriteTime": "2025-05-14T14:28:06+00:00"}, "yzDNNezH41u2eMmX8y/ArbDOSCpRJaFQCX/A5nPgGpI=": {"Identity": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "SourceId": "Saml2AuthDemo", "SourceType": "Discovered", "ContentRoot": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\", "BasePath": "_content/Saml2AuthDemo", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "nynt4yc5xr", "Integrity": "cZWhU8ntBevnbhNEh3hunnIoi6dE1eUwX41sB6+bHmo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "FileLength": 5227, "LastWriteTime": "2025-05-14T14:28:06+00:00"}, "3TGNKmwu3rNneqhaADG3QfvfEVCy9nhuK4Cb44+6Kyw=": {"Identity": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "SourceId": "Saml2AuthDemo", "SourceType": "Discovered", "ContentRoot": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\", "BasePath": "_content/Saml2AuthDemo", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "tgg2bl5mrw", "Integrity": "3e6awqXPijx918dzyzbbHKwaDBTsIQ8Us38QY30GRms=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "FileLength": 76483, "LastWriteTime": "2025-05-14T14:28:06+00:00"}, "dfaBbfvuUZx2ozmu1pb3AhHpBWTTT/+4253t6fhtnxs=": {"Identity": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "SourceId": "Saml2AuthDemo", "SourceType": "Discovered", "ContentRoot": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\", "BasePath": "_content/Saml2AuthDemo", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "brwg1hntyu", "Integrity": "1z7sRzwNyqpcTrqPd/iXuzoApqDdjpRbHwjAZ8QMhOk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "FileLength": 4028, "LastWriteTime": "2025-05-14T14:28:06+00:00"}, "e1flzdptWuaKcWc3BY9x2Ks73dcoVfYsIjExKMbO5wo=": {"Identity": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "SourceId": "Saml2AuthDemo", "SourceType": "Discovered", "ContentRoot": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\", "BasePath": "_content/Saml2AuthDemo", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "gf2dxac9qe", "Integrity": "dIm3VZXztwbIlhOzVt+ggg5Dvhp28MJQGJoweOH9cAE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "FileLength": 32461, "LastWriteTime": "2025-05-14T14:28:06+00:00"}, "Y75YrTAUjyq+CLbCB7gBl0Mz2A3A9eZMibdOwCaExGc=": {"Identity": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "SourceId": "Saml2AuthDemo", "SourceType": "Discovered", "ContentRoot": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\", "BasePath": "_content/Saml2AuthDemo", "RelativePath": "lib/bootstrap/dist/css/bootstrap#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "gawgt6fljy", "Integrity": "eEx7gvq+uEM0o4kUBiy/+Mxl6rHH9NQ9UzRBWHe9mXg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "FileLength": 202385, "LastWriteTime": "2025-05-14T14:28:06+00:00"}, "h98wc2zgjXbEYIWBjO2jM0yJJbelz4q2V/UhUYK5UWg=": {"Identity": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "SourceId": "Saml2AuthDemo", "SourceType": "Discovered", "ContentRoot": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\", "BasePath": "_content/Saml2AuthDemo", "RelativePath": "lib/bootstrap/dist/css/bootstrap.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "k9n1kkbua6", "Integrity": "CMAZj3JKoZUVYSxVPS/FeGatmYSvHkujo5oaZLNXx0o=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "FileLength": 492048, "LastWriteTime": "2025-05-14T14:28:06+00:00"}, "6qVoLQhn2JdS+bEgBAvbxwSw+xrJZHvAhFVRgaAm+xM=": {"Identity": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "SourceId": "Saml2AuthDemo", "SourceType": "Discovered", "ContentRoot": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\", "BasePath": "_content/Saml2AuthDemo", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "etnb7xlipe", "Integrity": "rldnE7wZYJj3Q43t5v8fg1ojKRwyt0Wtfm+224CacZs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "FileLength": 155764, "LastWriteTime": "2025-05-14T14:28:06+00:00"}, "NVW6coRfWts4P4EzqGbNkIM2ASk5VakCTuK2Ug5vtD0=": {"Identity": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "SourceId": "Saml2AuthDemo", "SourceType": "Discovered", "ContentRoot": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\", "BasePath": "_content/Saml2AuthDemo", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "kao5znno1s", "Integrity": "xMZ0SaSBYZSHVjFdZTAT/IjRExRIxSriWcJLcA9nkj0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "FileLength": 625953, "LastWriteTime": "2025-05-14T14:28:06+00:00"}, "OAMpU6uibxxGLFOee+4UHSez/ekoyrCYafoM1VBHmJo=": {"Identity": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "SourceId": "Saml2AuthDemo", "SourceType": "Discovered", "ContentRoot": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\", "BasePath": "_content/Saml2AuthDemo", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "u0biprgly9", "Integrity": "srIwGYgANrjaabGVuC3G7O0jv1Xh3Kt7dIc3/P0Ebf0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "FileLength": 229924, "LastWriteTime": "2025-05-14T14:28:06+00:00"}, "Y7xpcG5ZW08lcw/PPBgm5aOYLkox7ZAdhobcXyDzQpE=": {"Identity": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "SourceId": "Saml2AuthDemo", "SourceType": "Discovered", "ContentRoot": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\", "BasePath": "_content/Saml2AuthDemo", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "pxamm17y9e", "Integrity": "3UpdqvoTc6M2sug8WtFhr/m3tg+4zLMgoMgjqpn5n1I=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "FileLength": 402249, "LastWriteTime": "2025-05-14T14:28:06+00:00"}, "GW7snQOVhO9Nor6py1GKET4Sglj6gsV0h/G94kL1juc=": {"Identity": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "SourceId": "Saml2AuthDemo", "SourceType": "Discovered", "ContentRoot": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\", "BasePath": "_content/Saml2AuthDemo", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "hju<PERSON><PERSON>ly30", "Integrity": "XZfkOGd6FuhF88h5GgEmRIpXbm+hBkFo74yYDPY5rbw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "FileLength": 78641, "LastWriteTime": "2025-05-14T14:28:06+00:00"}, "onA1ExFey1yYg48t0bs4UItdlpKcuBgz08jcNis9lVU=": {"Identity": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "SourceId": "Saml2AuthDemo", "SourceType": "Discovered", "ContentRoot": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\", "BasePath": "_content/Saml2AuthDemo", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "u9xms436mi", "Integrity": "8i3JQdKYQQcJzmbkwhwY+1XPe7Utf1LdBnYZCvNmKWc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "FileLength": 311949, "LastWriteTime": "2025-05-14T14:28:06+00:00"}, "bgHg68gG+78+Sm8N4Kou98dHa6mLrD4jrXUzK+X4a1Q=": {"Identity": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "SourceId": "Saml2AuthDemo", "SourceType": "Discovered", "ContentRoot": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\", "BasePath": "_content/Saml2AuthDemo", "RelativePath": "lib/bootstrap/dist/js/bootstrap#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "48vr37mrsy", "Integrity": "LKpkBN2w3iudGRseLItcNcaMpI8qlSEUC7+DsnwGNwA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "FileLength": 136072, "LastWriteTime": "2025-05-14T14:28:06+00:00"}, "BjAeCksl6GOfiaTwT9hfkEb+HApywpvog1XE0Zi+9Bo=": {"Identity": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "SourceId": "Saml2AuthDemo", "SourceType": "Discovered", "ContentRoot": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\", "BasePath": "_content/Saml2AuthDemo", "RelativePath": "lib/bootstrap/dist/js/bootstrap.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "vaswmcjbo4", "Integrity": "R81NA2DWe8EPjZ2OUhieXYgvvXBnm78oMdeqOtSEr7c=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "FileLength": 250568, "LastWriteTime": "2025-05-14T14:28:06+00:00"}, "4TkGRgGCX/Wzs17jIanOMKffwa7da+QKuc3riUtH4Sc=": {"Identity": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "SourceId": "Saml2AuthDemo", "SourceType": "Discovered", "ContentRoot": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\", "BasePath": "_content/Saml2AuthDemo", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "zu238p5lxg", "Integrity": "O82ALp93hJ58HpPIcnn7uwTUWUnSvnmwNWbOrN4psVg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "FileLength": 58078, "LastWriteTime": "2025-05-14T14:28:06+00:00"}, "jftsEOrAioi02BZHTjvfkGHxvOwlKXKFd60x4/g6Lzg=": {"Identity": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "SourceId": "Saml2AuthDemo", "SourceType": "Discovered", "ContentRoot": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\", "BasePath": "_content/Saml2AuthDemo", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "sgi57dik4g", "Integrity": "vMfBbEXmojM9AaHrIyKSo+20n5JM7KMyJkBCfL4pgL4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "FileLength": 190253, "LastWriteTime": "2025-05-14T14:28:06+00:00"}, "3Ku4T+DtFwp4DS4TNb0wTQIFZVjawhPAY8UdcBAGdP8=": {"Identity": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\bootstrap\\LICENSE", "SourceId": "Saml2AuthDemo", "SourceType": "Discovered", "ContentRoot": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\", "BasePath": "_content/Saml2AuthDemo", "RelativePath": "lib/bootstrap/LICENSE#[.{fingerprint}]?", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "weyt030wr8", "Integrity": "iMA3h2QDzhvwmVtohviPpq8VwRv3UMY/PEE+ao7bBq0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\LICENSE", "FileLength": 1153, "LastWriteTime": "2025-05-14T14:28:06+00:00"}, "RHUDEcGF0X0jrjZG7iFmkSVMuUArHoQ7PxpzbaVJ0Hw=": {"Identity": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\jquery-validation-unobtrusive\\jquery.validate.unobtrusive.js", "SourceId": "Saml2AuthDemo", "SourceType": "Discovered", "ContentRoot": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\", "BasePath": "_content/Saml2AuthDemo", "RelativePath": "lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "dvc2bfcndg", "Integrity": "qbS02vMHZxdLNYKUtLPSYaSHXj1/ZwH1fv9f3XAY0LU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation-unobtrusive\\jquery.validate.unobtrusive.js", "FileLength": 19798, "LastWriteTime": "2025-05-14T14:28:06+00:00"}, "ZEckopBlManG4Lu5jgWhJuMz9sy0HKJWOKp+BTa+CEY=": {"Identity": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\jquery-validation-unobtrusive\\jquery.validate.unobtrusive.min.js", "SourceId": "Saml2AuthDemo", "SourceType": "Discovered", "ContentRoot": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\", "BasePath": "_content/Saml2AuthDemo", "RelativePath": "lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "gb7ocvbhts", "Integrity": "9GycpJnliUjJDVDqP0UEu/bsm9U+3dnQUH8+3W10vkY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation-unobtrusive\\jquery.validate.unobtrusive.min.js", "FileLength": 5871, "LastWriteTime": "2025-05-14T14:28:06+00:00"}, "f0qcLhBZpELs1WIsVDSY/lTM3RQV+a4YshG7PwTJ1K0=": {"Identity": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "SourceId": "Saml2AuthDemo", "SourceType": "Discovered", "ContentRoot": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\", "BasePath": "_content/Saml2AuthDemo", "RelativePath": "lib/jquery-validation-unobtrusive/LICENSE#[.{fingerprint}]?.txt", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "w<PERSON><PERSON><PERSON><PERSON><PERSON>", "Integrity": "aBc/n/nO9esvq0e80G57lEYLonvE5dKKUgoozaiVfLM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "FileLength": 587, "LastWriteTime": "2025-05-14T14:28:06+00:00"}, "T9tziKYf6vN55g5LY/Atq9mnynANn75t+F1HdgZohgg=": {"Identity": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "SourceId": "Saml2AuthDemo", "SourceType": "Discovered", "ContentRoot": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\", "BasePath": "_content/Saml2AuthDemo", "RelativePath": "lib/jquery-validation/dist/additional-methods#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "wus95c49fh", "Integrity": "qLDpf9Urms7R6rnASrjHz38WdQfOvSOmTgLDfzQSzIQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "FileLength": 43184, "LastWriteTime": "2025-05-14T14:28:06+00:00"}, "0WolaGCwbTWCGcYXZbK8Q1bhdVYqrcXjs4xM6zzUD8o=": {"Identity": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "SourceId": "Saml2AuthDemo", "SourceType": "Discovered", "ContentRoot": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\", "BasePath": "_content/Saml2AuthDemo", "RelativePath": "lib/jquery-validation/dist/additional-methods.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "sjab29p8z5", "Integrity": "2F/T6dcoSumcuA/fcU4W36VpSKPtq4nQf/0/vNFsC+w=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "FileLength": 18467, "LastWriteTime": "2025-05-14T14:28:06+00:00"}, "0MXbO4AQL9OFR6QCmm+YZAtq1QyIJF3Xa+UOgUtgXYc=": {"Identity": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "SourceId": "Saml2AuthDemo", "SourceType": "Discovered", "ContentRoot": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\", "BasePath": "_content/Saml2AuthDemo", "RelativePath": "lib/jquery-validation/dist/jquery.validate#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "d2pxujwhw3", "Integrity": "27gs04nyeNuL9zc/GLQLjdbZqhNGvH+xIYgnYVPIawE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "FileLength": 50276, "LastWriteTime": "2025-05-14T14:28:06+00:00"}, "QlKTzgF+4ehUz++EQmlRq1zEvqhqotl+y05bndx9Ktc=": {"Identity": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "SourceId": "Saml2AuthDemo", "SourceType": "Discovered", "ContentRoot": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\", "BasePath": "_content/Saml2AuthDemo", "RelativePath": "lib/jquery-validation/dist/jquery.validate.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "4dm14o4hmc", "Integrity": "eItLFOyfQ4d/OGzEnGchi2ZMVF8EhGgzS0k7fSOPifQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "FileLength": 23264, "LastWriteTime": "2025-05-14T14:28:06+00:00"}, "VfV37M+tRNPBAk8KvEPtA7hSaWddgYTd/D6eBEN04bU=": {"Identity": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\jquery-validation\\LICENSE.md", "SourceId": "Saml2AuthDemo", "SourceType": "Discovered", "ContentRoot": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\", "BasePath": "_content/Saml2AuthDemo", "RelativePath": "lib/jquery-validation/LICENSE#[.{fingerprint}]?.md", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "x0q3zqp4vz", "Integrity": "geHEkw/WGPdaHQMRq5HuNY9snliNzU/y2OW8ycnhGXw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\LICENSE.md", "FileLength": 1117, "LastWriteTime": "2025-05-14T14:28:06+00:00"}, "zQ2npQaNiekGpj/04xwH8UxilfkB/X0aiklzR2OkGRY=": {"Identity": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\jquery\\dist\\jquery.js", "SourceId": "Saml2AuthDemo", "SourceType": "Discovered", "ContentRoot": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\", "BasePath": "_content/Saml2AuthDemo", "RelativePath": "lib/jquery/dist/jquery#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "a8eee2794i", "Integrity": "igUc00PXGT1YBL1/Kf7QYy9fPlLqZKcEGrCqDz3EFDI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.js", "FileLength": 282115, "LastWriteTime": "2025-05-14T14:28:06+00:00"}, "JHF/waUdJwRN+IVz1fL9f5gqbRCEJxYQTf9NglR1P3U=": {"Identity": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\jquery\\dist\\jquery.min.js", "SourceId": "Saml2AuthDemo", "SourceType": "Discovered", "ContentRoot": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\", "BasePath": "_content/Saml2AuthDemo", "RelativePath": "lib/jquery/dist/jquery.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "fc9074g7ds", "Integrity": "T+aPohYXbm0fRYDpJLr+zJ9RmYTswGsahAoIsNiMld4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.min.js", "FileLength": 86929, "LastWriteTime": "2025-05-14T14:28:06+00:00"}, "lhRgZwh2ZmzrRbkfp+SKPiRiH/Maj7V5mg7fl+WMlFA=": {"Identity": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\jquery\\dist\\jquery.min.map", "SourceId": "Saml2AuthDemo", "SourceType": "Discovered", "ContentRoot": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\", "BasePath": "_content/Saml2AuthDemo", "RelativePath": "lib/jquery/dist/jquery.min#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "b804xvqo1w", "Integrity": "1eiGMA0y1kD5P/bXnTaiTKk8vtvQ8IC2nXAoI1HJXyE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.min.map", "FileLength": 132370, "LastWriteTime": "2025-05-14T14:28:06+00:00"}, "caxMjb35x+pDgqE/VUBnvzCglPOefvCKmEx4caN4DqQ=": {"Identity": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\jquery\\LICENSE.txt", "SourceId": "Saml2AuthDemo", "SourceType": "Discovered", "ContentRoot": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\", "BasePath": "_content/Saml2AuthDemo", "RelativePath": "lib/jquery/LICENSE#[.{fingerprint}]?.txt", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "afgyafcsqt", "Integrity": "WtKlDye/qV68IA2W1tHFTyXyc/HjQvQKBpIK1CEulFM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\LICENSE.txt", "FileLength": 1641, "LastWriteTime": "2025-05-14T14:28:06+00:00"}}, "CachedCopyCandidates": {}}