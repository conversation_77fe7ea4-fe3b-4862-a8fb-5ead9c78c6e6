{"Version": 1, "Hash": "lo1Qf/NxVfSqqmFgZtYafPls28GrxKMhWleDeQj5jHM=", "Source": "Saml2AuthDemo", "BasePath": "_content/Saml2AuthDemo", "Mode": "<PERSON><PERSON><PERSON>", "ManifestType": "Build", "ReferencedProjectsConfiguration": [], "DiscoveryPatterns": [{"Name": "Saml2AuthDemo\\wwwroot", "Source": "Saml2AuthDemo", "ContentRoot": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\", "BasePath": "_content/Saml2AuthDemo", "Pattern": "**"}], "Assets": [{"Identity": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\0c2qurj59r-90yqlj465b.gz", "SourceId": "Saml2AuthDemo", "SourceType": "Discovered", "ContentRoot": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Saml2AuthDemo", "RelativePath": "favicon#[.{fingerprint=90yqlj465b}]?.ico.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\favicon.ico", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "xngppdbdhq", "Integrity": "nR0lNhVJOqtsVYCBNYXOoTLPTwP88AJKUlf4evu9xkE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\favicon.ico", "FileLength": 9431, "LastWriteTime": "2025-05-29T06:01:30+00:00"}, {"Identity": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\0dvwnkaaa0-nynt4yc5xr.gz", "SourceId": "Saml2AuthDemo", "SourceType": "Discovered", "ContentRoot": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Saml2AuthDemo", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot#[.{fingerprint=nynt4yc5xr}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "yr6g57tbrs", "Integrity": "ski9h0PufafI6hMaaPSRPSY+iMBc1VbPoK1FYjUXHMQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "FileLength": 1737, "LastWriteTime": "2025-05-29T06:01:30+00:00"}, {"Identity": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\0kpipdbxfq-etnb7xlipe.gz", "SourceId": "Saml2AuthDemo", "SourceType": "Discovered", "ContentRoot": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Saml2AuthDemo", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min#[.{fingerprint=etnb7xlipe}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "fg8y3a0rbt", "Integrity": "T2rPGX2rORFBp2vX1wlC4q43KPqCjKbVkY+VHIDgmP0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "FileLength": 23242, "LastWriteTime": "2025-05-29T06:01:30+00:00"}, {"Identity": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\3l64p4di12-kao5znno1s.gz", "SourceId": "Saml2AuthDemo", "SourceType": "Discovered", "ContentRoot": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Saml2AuthDemo", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min.css#[.{fingerprint=kao5znno1s}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "py1k7jl89u", "Integrity": "8SkbHnWWeJ8KG1+bQUkOaPN31v/xtm4ZycUWB/SvS20=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "FileLength": 102069, "LastWriteTime": "2025-05-29T06:01:30+00:00"}, {"Identity": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\448jultfmw-zu238p5lxg.gz", "SourceId": "Saml2AuthDemo", "SourceType": "Discovered", "ContentRoot": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Saml2AuthDemo", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min#[.{fingerprint=zu238p5lxg}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "b9b8zdnevc", "Integrity": "/zGCumvrl91atAvEbBmyY+KKn65ToLyg+icn9FQP8xU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "FileLength": 15441, "LastWriteTime": "2025-05-29T06:01:30+00:00"}, {"Identity": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\5ikut97hnh-wus95c49fh.gz", "SourceId": "Saml2AuthDemo", "SourceType": "Discovered", "ContentRoot": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Saml2AuthDemo", "RelativePath": "lib/jquery-validation/dist/additional-methods#[.{fingerprint=wus95c49fh}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qur712kw8c", "Integrity": "Pk7e9oX4/+O7Ztv+d0Yk/GdqClCzkF9l6oJgJLD6RgU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "FileLength": 11698, "LastWriteTime": "2025-05-29T06:01:30+00:00"}, {"Identity": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\6lzqm95o0o-0i1dcxd824.gz", "SourceId": "Saml2AuthDemo", "SourceType": "Discovered", "ContentRoot": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Saml2AuthDemo", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.css#[.{fingerprint=0i1dcxd824}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ligbo5mwl1", "Integrity": "oytvk6SQ3cnlPxAF33gxI7GS06Rj2MrRoUPoiaFchjQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "FileLength": 27062, "LastWriteTime": "2025-05-29T06:01:30+00:00"}, {"Identity": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\7981pj8p43-x0q3zqp4vz.gz", "SourceId": "Saml2AuthDemo", "SourceType": "Discovered", "ContentRoot": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Saml2AuthDemo", "RelativePath": "lib/jquery-validation/LICENSE#[.{fingerprint=x0q3zqp4vz}]?.md.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\jquery-validation\\LICENSE.md", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "tbedkqp182", "Integrity": "yZU1lf/p2dEnCHUXYp8G4roAyod23x5MoR4DehwHfrA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\jquery-validation\\LICENSE.md", "FileLength": 670, "LastWriteTime": "2025-05-29T06:01:30+00:00"}, {"Identity": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\7iaxxtbiqn-b804xvqo1w.gz", "SourceId": "Saml2AuthDemo", "SourceType": "Discovered", "ContentRoot": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Saml2AuthDemo", "RelativePath": "lib/jquery/dist/jquery.min#[.{fingerprint=b804xvqo1w}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\jquery\\dist\\jquery.min.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4nebbfrnps", "Integrity": "4BWp/edO0rIyA+BxiKPHBnzmhoRLav1dEQiSlY7ixcc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\jquery\\dist\\jquery.min.map", "FileLength": 53014, "LastWriteTime": "2025-05-29T06:01:30+00:00"}, {"Identity": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\92bk6dxr5j-wqejeusuyq.gz", "SourceId": "Saml2AuthDemo", "SourceType": "Discovered", "ContentRoot": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Saml2AuthDemo", "RelativePath": "lib/jquery-validation-unobtrusive/LICENSE#[.{fingerprint=wqejeusuyq}]?.txt.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qmy2g5n6hh", "Integrity": "0k+GY7B37PlcFstK7JIg2ptnyI+BsfB92+jtjLIJOuo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "FileLength": 395, "LastWriteTime": "2025-05-29T06:01:30+00:00"}, {"Identity": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\9hizxlbrr4-sgi57dik4g.gz", "SourceId": "Saml2AuthDemo", "SourceType": "Discovered", "ContentRoot": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Saml2AuthDemo", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min.js#[.{fingerprint=sgi57dik4g}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "l0a66zixp4", "Integrity": "y/W+IS3pBW6YOxougP7451HTyQ/Q1y9J/D94i5Ds7u8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "FileLength": 46112, "LastWriteTime": "2025-05-29T06:01:30+00:00"}, {"Identity": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\9vs9glwrv0-fc9074g7ds.gz", "SourceId": "Saml2AuthDemo", "SourceType": "Discovered", "ContentRoot": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Saml2AuthDemo", "RelativePath": "lib/jquery/dist/jquery.min#[.{fingerprint=fc9074g7ds}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\jquery\\dist\\jquery.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "j3ly93bv7m", "Integrity": "T6QtgY4DAKUwGXkADndGXihd1P1yhbRrSJEJmozFCKQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\jquery\\dist\\jquery.min.js", "FileLength": 30358, "LastWriteTime": "2025-05-29T06:01:30+00:00"}, {"Identity": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\ae6gh6t3i1-jzb7jyrjvs.gz", "SourceId": "Saml2AuthDemo", "SourceType": "Discovered", "ContentRoot": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Saml2AuthDemo", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min.css#[.{fingerprint=jzb7jyrjvs}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "iez2ehrrlx", "Integrity": "ukFugDNptsdF2I0tTFVp+zrq3vCFvErB7PNxY2Pmf7s=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "FileLength": 13935, "LastWriteTime": "2025-05-29T06:01:30+00:00"}, {"Identity": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\ba2jr6qd4r-u9xms436mi.gz", "SourceId": "Saml2AuthDemo", "SourceType": "Discovered", "ContentRoot": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Saml2AuthDemo", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min.js#[.{fingerprint=u9xms436mi}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ka41v8yo7c", "Integrity": "BHUyrJ6l5FX9FTIykDazdjlP73jzpfplry3r+6zSdoE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "FileLength": 82041, "LastWriteTime": "2025-05-29T06:01:30+00:00"}, {"Identity": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\bt4acyjzvx-dvc2bfcndg.gz", "SourceId": "Saml2AuthDemo", "SourceType": "Discovered", "ContentRoot": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Saml2AuthDemo", "RelativePath": "lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive#[.{fingerprint=dvc2bfcndg}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\jquery-validation-unobtrusive\\jquery.validate.unobtrusive.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ia1fp3fsqk", "Integrity": "tra5GpUITUrALtnm990ZmXUgYSqsrYHJ2wct2dSXNwo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\jquery-validation-unobtrusive\\jquery.validate.unobtrusive.js", "FileLength": 4685, "LastWriteTime": "2025-05-29T06:01:30+00:00"}, {"Identity": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\cbjq4inumo-u0biprgly9.gz", "SourceId": "Saml2AuthDemo", "SourceType": "Discovered", "ContentRoot": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Saml2AuthDemo", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle#[.{fingerprint=u0biprgly9}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hy9fihjyo6", "Integrity": "KcZfC8VlRGpzBSf8kYjbjjvxZ1mENKljqzW5X1VIqCY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "FileLength": 48345, "LastWriteTime": "2025-05-29T06:01:30+00:00"}, {"Identity": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\cip0knvmfd-pxamm17y9e.gz", "SourceId": "Saml2AuthDemo", "SourceType": "Discovered", "ContentRoot": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Saml2AuthDemo", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.js#[.{fingerprint=pxamm17y9e}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "99x6gwa75b", "Integrity": "IyKBFJ2NdYfuuICMUQxeCcGk/bTVfEdW5iAvM9nw0oU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "FileLength": 91460, "LastWriteTime": "2025-05-29T06:01:30+00:00"}, {"Identity": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\cs0x98z2f0-d2pxujwhw3.gz", "SourceId": "Saml2AuthDemo", "SourceType": "Discovered", "ContentRoot": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Saml2AuthDemo", "RelativePath": "lib/jquery-validation/dist/jquery.validate#[.{fingerprint=d2pxujwhw3}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "scqjpphbl3", "Integrity": "LJlWNqc+gAs3U5I+jF4VyB+HKQmiECp60pjSAw/gqhs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "FileLength": 13101, "LastWriteTime": "2025-05-29T06:01:30+00:00"}, {"Identity": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\dunpfset9o-9n0ta5ieki.gz", "SourceId": "Saml2AuthDemo", "SourceType": "Discovered", "ContentRoot": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Saml2AuthDemo", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid#[.{fingerprint=9n0ta5ieki}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "xhmut9o5n8", "Integrity": "ZY60kbNkADk4KDRhW+LL+izegJBs9qdfvXorvDd7lIc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "FileLength": 6966, "LastWriteTime": "2025-05-29T06:01:30+00:00"}, {"Identity": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\dvdvw0odmf-sjab29p8z5.gz", "SourceId": "Saml2AuthDemo", "SourceType": "Discovered", "ContentRoot": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Saml2AuthDemo", "RelativePath": "lib/jquery-validation/dist/additional-methods.min#[.{fingerprint=sjab29p8z5}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "w2h719sd9u", "Integrity": "OLeOSYXppYtjegTqvAF1VW6jF7Rk/TBkhxwj44Fhlbc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "FileLength": 5460, "LastWriteTime": "2025-05-29T06:01:30+00:00"}, {"Identity": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\ey517g5lte-afgyafcsqt.gz", "SourceId": "Saml2AuthDemo", "SourceType": "Discovered", "ContentRoot": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Saml2AuthDemo", "RelativePath": "lib/jquery/LICENSE#[.{fingerprint=afgyafcsqt}]?.txt.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\jquery\\LICENSE.txt", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "em4b14lj27", "Integrity": "2tL6ssgUvPLUDr8vBWmWHtk+7IqutyuHFEGAG7jexBw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\jquery\\LICENSE.txt", "FileLength": 933, "LastWriteTime": "2025-05-29T06:01:30+00:00"}, {"Identity": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\gcqjvrmgyi-vxs71z90fw.gz", "SourceId": "Saml2AuthDemo", "SourceType": "Discovered", "ContentRoot": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Saml2AuthDemo", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min#[.{fingerprint=vxs71z90fw}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "otvwzz8k95", "Integrity": "HFjfcB/gV8FeYhFO87HnfGGK/Ebdg885i044QcdVncY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "FileLength": 6029, "LastWriteTime": "2025-05-29T06:01:30+00:00"}, {"Identity": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\k6ix4t2cun-a8eee2794i.gz", "SourceId": "Saml2AuthDemo", "SourceType": "Discovered", "ContentRoot": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Saml2AuthDemo", "RelativePath": "lib/jquery/dist/jquery#[.{fingerprint=a8eee2794i}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\jquery\\dist\\jquery.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "x92lfpy4r7", "Integrity": "xZFJpzAOm0oPUAPPaOaOm9PjEktxu2/+op37a3inY8Y=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\jquery\\dist\\jquery.js", "FileLength": 81613, "LastWriteTime": "2025-05-29T06:01:30+00:00"}, {"Identity": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\kwpabttjim-k9n1kkbua6.gz", "SourceId": "Saml2AuthDemo", "SourceType": "Discovered", "ContentRoot": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Saml2AuthDemo", "RelativePath": "lib/bootstrap/dist/css/bootstrap.css#[.{fingerprint=k9n1kkbua6}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "8nia3m3p3a", "Integrity": "HBgFcfSZJMq1nxRljAc5eNlMAVuXqU0lQoR9rBIy968=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "FileLength": 98033, "LastWriteTime": "2025-05-29T06:01:30+00:00"}, {"Identity": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\llfevw3361-0n9f8i66x6.gz", "SourceId": "Saml2AuthDemo", "SourceType": "Discovered", "ContentRoot": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Saml2AuthDemo", "RelativePath": "js/site#[.{fingerprint=0n9f8i66x6}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\js\\site.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "kimwdh415r", "Integrity": "/Mp0M0f41yhPFkzdAUe9swvvjRlvmhMty1mZZSRdmG0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\js\\site.js", "FileLength": 186, "LastWriteTime": "2025-05-29T06:01:30+00:00"}, {"Identity": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\lyme58ep3m-vaswmcjbo4.gz", "SourceId": "Saml2AuthDemo", "SourceType": "Discovered", "ContentRoot": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Saml2AuthDemo", "RelativePath": "lib/bootstrap/dist/js/bootstrap.js#[.{fingerprint=vaswmcjbo4}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "t7dzx6h33q", "Integrity": "V2nzFYIGfzW4T0n5xREU4Xq27AL4FR+ObKGcPUp3R8I=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "FileLength": 57786, "LastWriteTime": "2025-05-29T06:01:30+00:00"}, {"Identity": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\odolngyg4q-brwg1hntyu.gz", "SourceId": "Saml2AuthDemo", "SourceType": "Discovered", "ContentRoot": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Saml2AuthDemo", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min#[.{fingerprint=brwg1hntyu}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "450chlim9d", "Integrity": "SK/uC88mzOjgHwihIpIpaGtZcvPkQkQKkO0iusPs+Z0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "FileLength": 1615, "LastWriteTime": "2025-05-29T06:01:30+00:00"}, {"Identity": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\ovqzhavyxs-4dm14o4hmc.gz", "SourceId": "Saml2AuthDemo", "SourceType": "Discovered", "ContentRoot": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Saml2AuthDemo", "RelativePath": "lib/jquery-validation/dist/jquery.validate.min#[.{fingerprint=4dm14o4hmc}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "41f15l79m5", "Integrity": "WEtE86OkwYRC2CNB1ewkbZ+Kqi1+CF3HTu2hd7e2V+k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "FileLength": 7505, "LastWriteTime": "2025-05-29T06:01:30+00:00"}, {"Identity": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\s7f8b8w26r-48vr37mrsy.gz", "SourceId": "Saml2AuthDemo", "SourceType": "Discovered", "ContentRoot": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Saml2AuthDemo", "RelativePath": "lib/bootstrap/dist/js/bootstrap#[.{fingerprint=48vr37mrsy}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "gd33sbxxto", "Integrity": "1Ct5pGkD+FF0LlXrT4xzvGbCR79KcfwrOutx4z65BIc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "FileLength": 25283, "LastWriteTime": "2025-05-29T06:01:30+00:00"}, {"Identity": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\s7wjcvroec-gf2dxac9qe.gz", "SourceId": "Saml2AuthDemo", "SourceType": "Discovered", "ContentRoot": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Saml2AuthDemo", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min.css#[.{fingerprint=gf2dxac9qe}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "m8laq02kqt", "Integrity": "+vu0DGLWqkGPTrKaf7U22OpO4XImAF0LW8HvFHEnCKA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "FileLength": 8039, "LastWriteTime": "2025-05-29T06:01:30+00:00"}, {"Identity": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\uucirysn9r-hdhjllpya4.gz", "SourceId": "Saml2AuthDemo", "SourceType": "Discovered", "ContentRoot": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Saml2AuthDemo", "RelativePath": "css/site#[.{fingerprint=hdhjllpya4}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\css\\site.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "0tevtyurkr", "Integrity": "TP7j5uZuJdntcOfcko7umOSdXFMdhRUKfjFd0AWnaFI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\css\\site.css", "FileLength": 641, "LastWriteTime": "2025-05-29T06:01:30+00:00"}, {"Identity": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\xd5r9ktl5s-gb7ocvbhts.gz", "SourceId": "Saml2AuthDemo", "SourceType": "Discovered", "ContentRoot": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Saml2AuthDemo", "RelativePath": "lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.min#[.{fingerprint=gb7ocvbhts}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\jquery-validation-unobtrusive\\jquery.validate.unobtrusive.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "y5r6ekb0nn", "Integrity": "+uJmiWNDfG3y+8SUapZ3/8iFhrZEO4UoNTds6PJqncM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\jquery-validation-unobtrusive\\jquery.validate.unobtrusive.min.js", "FileLength": 2220, "LastWriteTime": "2025-05-29T06:01:30+00:00"}, {"Identity": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\xl2utgi5t6-gawgt6fljy.gz", "SourceId": "Saml2AuthDemo", "SourceType": "Discovered", "ContentRoot": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Saml2AuthDemo", "RelativePath": "lib/bootstrap/dist/css/bootstrap#[.{fingerprint=gawgt6fljy}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "20hnowj6dn", "Integrity": "LgKX0350Ug6XsDByPQMOQx1sihJrlzqNLL47LiyoSQ4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "FileLength": 25746, "LastWriteTime": "2025-05-29T06:01:30+00:00"}, {"Identity": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\ye5ch5e0ju-tgg2bl5mrw.gz", "SourceId": "Saml2AuthDemo", "SourceType": "Discovered", "ContentRoot": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Saml2AuthDemo", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.css#[.{fingerprint=tgg2bl5mrw}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2shzy9x2jj", "Integrity": "PgWj9lwXprPCKRJlELXHPizyFeevIvOzAMOrcCGtBdI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "FileLength": 17007, "LastWriteTime": "2025-05-29T06:01:30+00:00"}, {"Identity": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\zo36bj6qtt-hjuzisly30.gz", "SourceId": "Saml2AuthDemo", "SourceType": "Discovered", "ContentRoot": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Saml2AuthDemo", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min#[.{fingerprint=hjuzisly30}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "0nrdyyg5oh", "Integrity": "1MiwfybWPUf85W8WHsUjrjh6SWZrvKae/f829+FAkhU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "FileLength": 22298, "LastWriteTime": "2025-05-29T06:01:30+00:00"}, {"Identity": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\css\\site.css", "SourceId": "Saml2AuthDemo", "SourceType": "Discovered", "ContentRoot": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\", "BasePath": "_content/Saml2AuthDemo", "RelativePath": "css/site#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "hdhjllpya4", "Integrity": "S2ihmzMFFc3FWmBWsR+NiddZWa8kbyaQYBx2FDkIoHs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\site.css", "FileLength": 1417, "LastWriteTime": "2025-05-14T14:28:06+00:00"}, {"Identity": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\favicon.ico", "SourceId": "Saml2AuthDemo", "SourceType": "Discovered", "ContentRoot": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\", "BasePath": "_content/Saml2AuthDemo", "RelativePath": "favicon#[.{fingerprint}]?.ico", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "90yqlj465b", "Integrity": "qU+KhVPK6oQw3UyjzAHU4xjRmCj3TLZUU/+39dni9E0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\favicon.ico", "FileLength": 32038, "LastWriteTime": "2025-05-14T14:28:06+00:00"}, {"Identity": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\js\\site.js", "SourceId": "Saml2AuthDemo", "SourceType": "Discovered", "ContentRoot": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\", "BasePath": "_content/Saml2AuthDemo", "RelativePath": "js/site#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "0n9f8i66x6", "Integrity": "dLGP40S79Xnx6GqUthRF6NWvjvhQ1nOvdVSwaNcgG18=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\site.js", "FileLength": 230, "LastWriteTime": "2025-05-14T14:28:06+00:00"}, {"Identity": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "SourceId": "Saml2AuthDemo", "SourceType": "Discovered", "ContentRoot": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\", "BasePath": "_content/Saml2AuthDemo", "RelativePath": "lib/bootstrap/dist/css/bootstrap#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "gawgt6fljy", "Integrity": "eEx7gvq+uEM0o4kUBiy/+Mxl6rHH9NQ9UzRBWHe9mXg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "FileLength": 202385, "LastWriteTime": "2025-05-14T14:28:06+00:00"}, {"Identity": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "SourceId": "Saml2AuthDemo", "SourceType": "Discovered", "ContentRoot": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\", "BasePath": "_content/Saml2AuthDemo", "RelativePath": "lib/bootstrap/dist/css/bootstrap.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "k9n1kkbua6", "Integrity": "CMAZj3JKoZUVYSxVPS/FeGatmYSvHkujo5oaZLNXx0o=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "FileLength": 492048, "LastWriteTime": "2025-05-14T14:28:06+00:00"}, {"Identity": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "SourceId": "Saml2AuthDemo", "SourceType": "Discovered", "ContentRoot": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\", "BasePath": "_content/Saml2AuthDemo", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "etnb7xlipe", "Integrity": "rldnE7wZYJj3Q43t5v8fg1ojKRwyt0Wtfm+224CacZs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "FileLength": 155764, "LastWriteTime": "2025-05-14T14:28:06+00:00"}, {"Identity": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "SourceId": "Saml2AuthDemo", "SourceType": "Discovered", "ContentRoot": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\", "BasePath": "_content/Saml2AuthDemo", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "kao5znno1s", "Integrity": "xMZ0SaSBYZSHVjFdZTAT/IjRExRIxSriWcJLcA9nkj0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "FileLength": 625953, "LastWriteTime": "2025-05-14T14:28:06+00:00"}, {"Identity": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "SourceId": "Saml2AuthDemo", "SourceType": "Discovered", "ContentRoot": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\", "BasePath": "_content/Saml2AuthDemo", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "9n0ta5ieki", "Integrity": "ezlzqfTqJZTjEDR18neKc2tUTZtWoz3oQ/ouWY9WmO8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "FileLength": 68266, "LastWriteTime": "2025-05-14T14:28:06+00:00"}, {"Identity": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "SourceId": "Saml2AuthDemo", "SourceType": "Discovered", "ContentRoot": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\", "BasePath": "_content/Saml2AuthDemo", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "0i1dcxd824", "Integrity": "2MgjO0zpqYZscatQSGWJ9Io9U8EjvXk0iYygYz1Q+Ms=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "FileLength": 151749, "LastWriteTime": "2025-05-14T14:28:06+00:00"}, {"Identity": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "SourceId": "Saml2AuthDemo", "SourceType": "Discovered", "ContentRoot": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\", "BasePath": "_content/Saml2AuthDemo", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "vxs71z90fw", "Integrity": "vdSFRAWr6LTognRmxyi6QlSO5O+MC+VGyMbziTrBmBQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "FileLength": 48494, "LastWriteTime": "2025-05-14T14:28:06+00:00"}, {"Identity": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "SourceId": "Saml2AuthDemo", "SourceType": "Discovered", "ContentRoot": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\", "BasePath": "_content/Saml2AuthDemo", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "jzb7jyrjvs", "Integrity": "kZzlXLpTC0WvL0AL7nXf07BJ28WnY/1H/HrKgS68Q4I=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "FileLength": 108539, "LastWriteTime": "2025-05-14T14:28:06+00:00"}, {"Identity": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "SourceId": "Saml2AuthDemo", "SourceType": "Discovered", "ContentRoot": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\", "BasePath": "_content/Saml2AuthDemo", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "nynt4yc5xr", "Integrity": "cZWhU8ntBevnbhNEh3hunnIoi6dE1eUwX41sB6+bHmo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "FileLength": 5227, "LastWriteTime": "2025-05-14T14:28:06+00:00"}, {"Identity": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "SourceId": "Saml2AuthDemo", "SourceType": "Discovered", "ContentRoot": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\", "BasePath": "_content/Saml2AuthDemo", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "tgg2bl5mrw", "Integrity": "3e6awqXPijx918dzyzbbHKwaDBTsIQ8Us38QY30GRms=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "FileLength": 76483, "LastWriteTime": "2025-05-14T14:28:06+00:00"}, {"Identity": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "SourceId": "Saml2AuthDemo", "SourceType": "Discovered", "ContentRoot": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\", "BasePath": "_content/Saml2AuthDemo", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "brwg1hntyu", "Integrity": "1z7sRzwNyqpcTrqPd/iXuzoApqDdjpRbHwjAZ8QMhOk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "FileLength": 4028, "LastWriteTime": "2025-05-14T14:28:06+00:00"}, {"Identity": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "SourceId": "Saml2AuthDemo", "SourceType": "Discovered", "ContentRoot": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\", "BasePath": "_content/Saml2AuthDemo", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "gf2dxac9qe", "Integrity": "dIm3VZXztwbIlhOzVt+ggg5Dvhp28MJQGJoweOH9cAE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "FileLength": 32461, "LastWriteTime": "2025-05-14T14:28:06+00:00"}, {"Identity": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "SourceId": "Saml2AuthDemo", "SourceType": "Discovered", "ContentRoot": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\", "BasePath": "_content/Saml2AuthDemo", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "u0biprgly9", "Integrity": "srIwGYgANrjaabGVuC3G7O0jv1Xh3Kt7dIc3/P0Ebf0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "FileLength": 229924, "LastWriteTime": "2025-05-14T14:28:06+00:00"}, {"Identity": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "SourceId": "Saml2AuthDemo", "SourceType": "Discovered", "ContentRoot": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\", "BasePath": "_content/Saml2AuthDemo", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "pxamm17y9e", "Integrity": "3UpdqvoTc6M2sug8WtFhr/m3tg+4zLMgoMgjqpn5n1I=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "FileLength": 402249, "LastWriteTime": "2025-05-14T14:28:06+00:00"}, {"Identity": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "SourceId": "Saml2AuthDemo", "SourceType": "Discovered", "ContentRoot": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\", "BasePath": "_content/Saml2AuthDemo", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "hju<PERSON><PERSON>ly30", "Integrity": "XZfkOGd6FuhF88h5GgEmRIpXbm+hBkFo74yYDPY5rbw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "FileLength": 78641, "LastWriteTime": "2025-05-14T14:28:06+00:00"}, {"Identity": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "SourceId": "Saml2AuthDemo", "SourceType": "Discovered", "ContentRoot": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\", "BasePath": "_content/Saml2AuthDemo", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "u9xms436mi", "Integrity": "8i3JQdKYQQcJzmbkwhwY+1XPe7Utf1LdBnYZCvNmKWc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "FileLength": 311949, "LastWriteTime": "2025-05-14T14:28:06+00:00"}, {"Identity": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "SourceId": "Saml2AuthDemo", "SourceType": "Discovered", "ContentRoot": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\", "BasePath": "_content/Saml2AuthDemo", "RelativePath": "lib/bootstrap/dist/js/bootstrap#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "48vr37mrsy", "Integrity": "LKpkBN2w3iudGRseLItcNcaMpI8qlSEUC7+DsnwGNwA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "FileLength": 136072, "LastWriteTime": "2025-05-14T14:28:06+00:00"}, {"Identity": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "SourceId": "Saml2AuthDemo", "SourceType": "Discovered", "ContentRoot": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\", "BasePath": "_content/Saml2AuthDemo", "RelativePath": "lib/bootstrap/dist/js/bootstrap.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "vaswmcjbo4", "Integrity": "R81NA2DWe8EPjZ2OUhieXYgvvXBnm78oMdeqOtSEr7c=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "FileLength": 250568, "LastWriteTime": "2025-05-14T14:28:06+00:00"}, {"Identity": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "SourceId": "Saml2AuthDemo", "SourceType": "Discovered", "ContentRoot": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\", "BasePath": "_content/Saml2AuthDemo", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "zu238p5lxg", "Integrity": "O82ALp93hJ58HpPIcnn7uwTUWUnSvnmwNWbOrN4psVg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "FileLength": 58078, "LastWriteTime": "2025-05-14T14:28:06+00:00"}, {"Identity": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "SourceId": "Saml2AuthDemo", "SourceType": "Discovered", "ContentRoot": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\", "BasePath": "_content/Saml2AuthDemo", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "sgi57dik4g", "Integrity": "vMfBbEXmojM9AaHrIyKSo+20n5JM7KMyJkBCfL4pgL4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "FileLength": 190253, "LastWriteTime": "2025-05-14T14:28:06+00:00"}, {"Identity": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\bootstrap\\LICENSE", "SourceId": "Saml2AuthDemo", "SourceType": "Discovered", "ContentRoot": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\", "BasePath": "_content/Saml2AuthDemo", "RelativePath": "lib/bootstrap/LICENSE#[.{fingerprint}]?", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "weyt030wr8", "Integrity": "iMA3h2QDzhvwmVtohviPpq8VwRv3UMY/PEE+ao7bBq0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\LICENSE", "FileLength": 1153, "LastWriteTime": "2025-05-14T14:28:06+00:00"}, {"Identity": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\jquery\\dist\\jquery.js", "SourceId": "Saml2AuthDemo", "SourceType": "Discovered", "ContentRoot": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\", "BasePath": "_content/Saml2AuthDemo", "RelativePath": "lib/jquery/dist/jquery#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "a8eee2794i", "Integrity": "igUc00PXGT1YBL1/Kf7QYy9fPlLqZKcEGrCqDz3EFDI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.js", "FileLength": 282115, "LastWriteTime": "2025-05-14T14:28:06+00:00"}, {"Identity": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\jquery\\dist\\jquery.min.js", "SourceId": "Saml2AuthDemo", "SourceType": "Discovered", "ContentRoot": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\", "BasePath": "_content/Saml2AuthDemo", "RelativePath": "lib/jquery/dist/jquery.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "fc9074g7ds", "Integrity": "T+aPohYXbm0fRYDpJLr+zJ9RmYTswGsahAoIsNiMld4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.min.js", "FileLength": 86929, "LastWriteTime": "2025-05-14T14:28:06+00:00"}, {"Identity": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\jquery\\dist\\jquery.min.map", "SourceId": "Saml2AuthDemo", "SourceType": "Discovered", "ContentRoot": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\", "BasePath": "_content/Saml2AuthDemo", "RelativePath": "lib/jquery/dist/jquery.min#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "b804xvqo1w", "Integrity": "1eiGMA0y1kD5P/bXnTaiTKk8vtvQ8IC2nXAoI1HJXyE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.min.map", "FileLength": 132370, "LastWriteTime": "2025-05-14T14:28:06+00:00"}, {"Identity": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\jquery\\LICENSE.txt", "SourceId": "Saml2AuthDemo", "SourceType": "Discovered", "ContentRoot": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\", "BasePath": "_content/Saml2AuthDemo", "RelativePath": "lib/jquery/LICENSE#[.{fingerprint}]?.txt", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "afgyafcsqt", "Integrity": "WtKlDye/qV68IA2W1tHFTyXyc/HjQvQKBpIK1CEulFM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\LICENSE.txt", "FileLength": 1641, "LastWriteTime": "2025-05-14T14:28:06+00:00"}, {"Identity": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "SourceId": "Saml2AuthDemo", "SourceType": "Discovered", "ContentRoot": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\", "BasePath": "_content/Saml2AuthDemo", "RelativePath": "lib/jquery-validation/dist/additional-methods#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "wus95c49fh", "Integrity": "qLDpf9Urms7R6rnASrjHz38WdQfOvSOmTgLDfzQSzIQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "FileLength": 43184, "LastWriteTime": "2025-05-14T14:28:06+00:00"}, {"Identity": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "SourceId": "Saml2AuthDemo", "SourceType": "Discovered", "ContentRoot": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\", "BasePath": "_content/Saml2AuthDemo", "RelativePath": "lib/jquery-validation/dist/additional-methods.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "sjab29p8z5", "Integrity": "2F/T6dcoSumcuA/fcU4W36VpSKPtq4nQf/0/vNFsC+w=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "FileLength": 18467, "LastWriteTime": "2025-05-14T14:28:06+00:00"}, {"Identity": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "SourceId": "Saml2AuthDemo", "SourceType": "Discovered", "ContentRoot": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\", "BasePath": "_content/Saml2AuthDemo", "RelativePath": "lib/jquery-validation/dist/jquery.validate#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "d2pxujwhw3", "Integrity": "27gs04nyeNuL9zc/GLQLjdbZqhNGvH+xIYgnYVPIawE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "FileLength": 50276, "LastWriteTime": "2025-05-14T14:28:06+00:00"}, {"Identity": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "SourceId": "Saml2AuthDemo", "SourceType": "Discovered", "ContentRoot": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\", "BasePath": "_content/Saml2AuthDemo", "RelativePath": "lib/jquery-validation/dist/jquery.validate.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "4dm14o4hmc", "Integrity": "eItLFOyfQ4d/OGzEnGchi2ZMVF8EhGgzS0k7fSOPifQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "FileLength": 23264, "LastWriteTime": "2025-05-14T14:28:06+00:00"}, {"Identity": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\jquery-validation\\LICENSE.md", "SourceId": "Saml2AuthDemo", "SourceType": "Discovered", "ContentRoot": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\", "BasePath": "_content/Saml2AuthDemo", "RelativePath": "lib/jquery-validation/LICENSE#[.{fingerprint}]?.md", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "x0q3zqp4vz", "Integrity": "geHEkw/WGPdaHQMRq5HuNY9snliNzU/y2OW8ycnhGXw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\LICENSE.md", "FileLength": 1117, "LastWriteTime": "2025-05-14T14:28:06+00:00"}, {"Identity": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\jquery-validation-unobtrusive\\jquery.validate.unobtrusive.js", "SourceId": "Saml2AuthDemo", "SourceType": "Discovered", "ContentRoot": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\", "BasePath": "_content/Saml2AuthDemo", "RelativePath": "lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "dvc2bfcndg", "Integrity": "qbS02vMHZxdLNYKUtLPSYaSHXj1/ZwH1fv9f3XAY0LU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation-unobtrusive\\jquery.validate.unobtrusive.js", "FileLength": 19798, "LastWriteTime": "2025-05-14T14:28:06+00:00"}, {"Identity": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\jquery-validation-unobtrusive\\jquery.validate.unobtrusive.min.js", "SourceId": "Saml2AuthDemo", "SourceType": "Discovered", "ContentRoot": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\", "BasePath": "_content/Saml2AuthDemo", "RelativePath": "lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "gb7ocvbhts", "Integrity": "9GycpJnliUjJDVDqP0UEu/bsm9U+3dnQUH8+3W10vkY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation-unobtrusive\\jquery.validate.unobtrusive.min.js", "FileLength": 5871, "LastWriteTime": "2025-05-14T14:28:06+00:00"}, {"Identity": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "SourceId": "Saml2AuthDemo", "SourceType": "Discovered", "ContentRoot": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\", "BasePath": "_content/Saml2AuthDemo", "RelativePath": "lib/jquery-validation-unobtrusive/LICENSE#[.{fingerprint}]?.txt", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "w<PERSON><PERSON><PERSON><PERSON><PERSON>", "Integrity": "aBc/n/nO9esvq0e80G57lEYLonvE5dKKUgoozaiVfLM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "FileLength": 587, "LastWriteTime": "2025-05-14T14:28:06+00:00"}], "Endpoints": [{"Route": "css/site.css", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\uucirysn9r-hdhjllpya4.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.001557632399"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "641"}, {"Name": "ETag", "Value": "\"TP7j5uZuJdntcOfcko7umOSdXFMdhRUKfjFd0AWnaFI=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 06:01:30 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"S2ihmzMFFc3FWmBWsR+NiddZWa8kbyaQYBx2FDkIoHs=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-S2ihmzMFFc3FWmBWsR+NiddZWa8kbyaQYBx2FDkIoHs="}]}, {"Route": "css/site.css", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\css\\site.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1417"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"S2ihmzMFFc3FWmBWsR+NiddZWa8kbyaQYBx2FDkIoHs=\""}, {"Name": "Last-Modified", "Value": "Wed, 14 May 2025 14:28:06 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-S2ihmzMFFc3FWmBWsR+NiddZWa8kbyaQYBx2FDkIoHs="}]}, {"Route": "css/site.css.gz", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\uucirysn9r-hdhjllpya4.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "641"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"TP7j5uZuJdntcOfcko7umOSdXFMdhRUKfjFd0AWnaFI=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 06:01:30 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-TP7j5uZuJdntcOfcko7umOSdXFMdhRUKfjFd0AWnaFI="}]}, {"Route": "css/site.hdhjllpya4.css", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\uucirysn9r-hdhjllpya4.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.001557632399"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "641"}, {"Name": "ETag", "Value": "\"TP7j5uZuJdntcOfcko7umOSdXFMdhRUKfjFd0AWnaFI=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 06:01:30 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"S2ihmzMFFc3FWmBWsR+NiddZWa8kbyaQYBx2FDkIoHs=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "hdhjllpya4"}, {"Name": "label", "Value": "css/site.css"}, {"Name": "integrity", "Value": "sha256-S2ihmzMFFc3FWmBWsR+NiddZWa8kbyaQYBx2FDkIoHs="}]}, {"Route": "css/site.hdhjllpya4.css", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\css\\site.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1417"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"S2ihmzMFFc3FWmBWsR+NiddZWa8kbyaQYBx2FDkIoHs=\""}, {"Name": "Last-Modified", "Value": "Wed, 14 May 2025 14:28:06 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "hdhjllpya4"}, {"Name": "label", "Value": "css/site.css"}, {"Name": "integrity", "Value": "sha256-S2ihmzMFFc3FWmBWsR+NiddZWa8kbyaQYBx2FDkIoHs="}]}, {"Route": "css/site.hdhjllpya4.css.gz", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\uucirysn9r-hdhjllpya4.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "641"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"TP7j5uZuJdntcOfcko7umOSdXFMdhRUKfjFd0AWnaFI=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 06:01:30 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "hdhjllpya4"}, {"Name": "label", "Value": "css/site.css.gz"}, {"Name": "integrity", "Value": "sha256-TP7j5uZuJdntcOfcko7umOSdXFMdhRUKfjFd0AWnaFI="}]}, {"Route": "favicon.90yqlj465b.ico", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\0c2qurj59r-90yqlj465b.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000106022053"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "9431"}, {"Name": "ETag", "Value": "\"nR0lNhVJOqtsVYCBNYXOoTLPTwP88AJKUlf4evu9xkE=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 06:01:30 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "image/x-icon"}, {"Name": "ETag", "Value": "W/\"qU+KhVPK6oQw3UyjzAHU4xjRmCj3TLZUU/+39dni9E0=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "90yqlj465b"}, {"Name": "label", "Value": "favicon.ico"}, {"Name": "integrity", "Value": "sha256-qU+KhVPK6oQw3UyjzAHU4xjRmCj3TLZUU/+39dni9E0="}]}, {"Route": "favicon.90yqlj465b.ico", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\favicon.ico", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "32038"}, {"Name": "Content-Type", "Value": "image/x-icon"}, {"Name": "ETag", "Value": "\"qU+KhVPK6oQw3UyjzAHU4xjRmCj3TLZUU/+39dni9E0=\""}, {"Name": "Last-Modified", "Value": "Wed, 14 May 2025 14:28:06 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "90yqlj465b"}, {"Name": "label", "Value": "favicon.ico"}, {"Name": "integrity", "Value": "sha256-qU+KhVPK6oQw3UyjzAHU4xjRmCj3TLZUU/+39dni9E0="}]}, {"Route": "favicon.90yqlj465b.ico.gz", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\0c2qurj59r-90yqlj465b.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "9431"}, {"Name": "Content-Type", "Value": "image/x-icon"}, {"Name": "ETag", "Value": "\"nR0lNhVJOqtsVYCBNYXOoTLPTwP88AJKUlf4evu9xkE=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 06:01:30 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "90yqlj465b"}, {"Name": "label", "Value": "favicon.ico.gz"}, {"Name": "integrity", "Value": "sha256-nR0lNhVJOqtsVYCBNYXOoTLPTwP88AJKUlf4evu9xkE="}]}, {"Route": "favicon.ico", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\0c2qurj59r-90yqlj465b.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000106022053"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "9431"}, {"Name": "ETag", "Value": "\"nR0lNhVJOqtsVYCBNYXOoTLPTwP88AJKUlf4evu9xkE=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 06:01:30 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "image/x-icon"}, {"Name": "ETag", "Value": "W/\"qU+KhVPK6oQw3UyjzAHU4xjRmCj3TLZUU/+39dni9E0=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-qU+KhVPK6oQw3UyjzAHU4xjRmCj3TLZUU/+39dni9E0="}]}, {"Route": "favicon.ico", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\favicon.ico", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "32038"}, {"Name": "Content-Type", "Value": "image/x-icon"}, {"Name": "ETag", "Value": "\"qU+KhVPK6oQw3UyjzAHU4xjRmCj3TLZUU/+39dni9E0=\""}, {"Name": "Last-Modified", "Value": "Wed, 14 May 2025 14:28:06 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-qU+KhVPK6oQw3UyjzAHU4xjRmCj3TLZUU/+39dni9E0="}]}, {"Route": "favicon.ico.gz", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\0c2qurj59r-90yqlj465b.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "9431"}, {"Name": "Content-Type", "Value": "image/x-icon"}, {"Name": "ETag", "Value": "\"nR0lNhVJOqtsVYCBNYXOoTLPTwP88AJKUlf4evu9xkE=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 06:01:30 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-nR0lNhVJOqtsVYCBNYXOoTLPTwP88AJKUlf4evu9xkE="}]}, {"Route": "js/site.0n9f8i66x6.js", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\llfevw3361-0n9f8i66x6.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.005347593583"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "186"}, {"Name": "ETag", "Value": "\"/Mp0M0f41yhPFkzdAUe9swvvjRlvmhMty1mZZSRdmG0=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 06:01:30 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"dLGP40S79Xnx6GqUthRF6NWvjvhQ1nOvdVSwaNcgG18=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "0n9f8i66x6"}, {"Name": "label", "Value": "js/site.js"}, {"Name": "integrity", "Value": "sha256-dLGP40S79Xnx6GqUthRF6NWvjvhQ1nOvdVSwaNcgG18="}]}, {"Route": "js/site.0n9f8i66x6.js", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\js\\site.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "230"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"dLGP40S79Xnx6GqUthRF6NWvjvhQ1nOvdVSwaNcgG18=\""}, {"Name": "Last-Modified", "Value": "Wed, 14 May 2025 14:28:06 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "0n9f8i66x6"}, {"Name": "label", "Value": "js/site.js"}, {"Name": "integrity", "Value": "sha256-dLGP40S79Xnx6GqUthRF6NWvjvhQ1nOvdVSwaNcgG18="}]}, {"Route": "js/site.0n9f8i66x6.js.gz", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\llfevw3361-0n9f8i66x6.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "186"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"/Mp0M0f41yhPFkzdAUe9swvvjRlvmhMty1mZZSRdmG0=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 06:01:30 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "0n9f8i66x6"}, {"Name": "label", "Value": "js/site.js.gz"}, {"Name": "integrity", "Value": "sha256-/Mp0M0f41yhPFkzdAUe9swvvjRlvmhMty1mZZSRdmG0="}]}, {"Route": "js/site.js", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\llfevw3361-0n9f8i66x6.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.005347593583"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "186"}, {"Name": "ETag", "Value": "\"/Mp0M0f41yhPFkzdAUe9swvvjRlvmhMty1mZZSRdmG0=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 06:01:30 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"dLGP40S79Xnx6GqUthRF6NWvjvhQ1nOvdVSwaNcgG18=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-dLGP40S79Xnx6GqUthRF6NWvjvhQ1nOvdVSwaNcgG18="}]}, {"Route": "js/site.js", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\js\\site.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "230"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"dLGP40S79Xnx6GqUthRF6NWvjvhQ1nOvdVSwaNcgG18=\""}, {"Name": "Last-Modified", "Value": "Wed, 14 May 2025 14:28:06 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-dLGP40S79Xnx6GqUthRF6NWvjvhQ1nOvdVSwaNcgG18="}]}, {"Route": "js/site.js.gz", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\llfevw3361-0n9f8i66x6.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "186"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"/Mp0M0f41yhPFkzdAUe9swvvjRlvmhMty1mZZSRdmG0=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 06:01:30 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-/Mp0M0f41yhPFkzdAUe9swvvjRlvmhMty1mZZSRdmG0="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.css", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\xl2utgi5t6-gawgt6fljy.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000038839476"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "25746"}, {"Name": "ETag", "Value": "\"LgKX0350Ug6XsDByPQMOQx1sihJrlzqNLL47LiyoSQ4=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 06:01:30 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"eEx7gvq+uEM0o4kUBiy/+Mxl6rHH9NQ9UzRBWHe9mXg=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-eEx7gvq+uEM0o4kUBiy/+Mxl6rHH9NQ9UzRBWHe9mXg="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.css", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "202385"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"eEx7gvq+uEM0o4kUBiy/+Mxl6rHH9NQ9UzRBWHe9mXg=\""}, {"Name": "Last-Modified", "Value": "Wed, 14 May 2025 14:28:06 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-eEx7gvq+uEM0o4kUBiy/+Mxl6rHH9NQ9UzRBWHe9mXg="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.css.gz", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\xl2utgi5t6-gawgt6fljy.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "25746"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"LgKX0350Ug6XsDByPQMOQx1sihJrlzqNLL47LiyoSQ4=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 06:01:30 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-LgKX0350Ug6XsDByPQMOQx1sihJrlzqNLL47LiyoSQ4="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.css.k9n1kkbua6.map", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\kwpabttjim-k9n1kkbua6.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000010200543"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "98033"}, {"Name": "ETag", "Value": "\"HBgFcfSZJMq1nxRljAc5eNlMAVuXqU0lQoR9rBIy968=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 06:01:30 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"CMAZj3JKoZUVYSxVPS/FeGatmYSvHkujo5oaZLNXx0o=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "k9n1kkbua6"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.css.map"}, {"Name": "integrity", "Value": "sha256-CMAZj3JKoZUVYSxVPS/FeGatmYSvHkujo5oaZLNXx0o="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.css.k9n1kkbua6.map", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "492048"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"CMAZj3JKoZUVYSxVPS/FeGatmYSvHkujo5oaZLNXx0o=\""}, {"Name": "Last-Modified", "Value": "Wed, 14 May 2025 14:28:06 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "k9n1kkbua6"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.css.map"}, {"Name": "integrity", "Value": "sha256-CMAZj3JKoZUVYSxVPS/FeGatmYSvHkujo5oaZLNXx0o="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.css.k9n1kkbua6.map.gz", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\kwpabttjim-k9n1kkbua6.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "98033"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"HBgFcfSZJMq1nxRljAc5eNlMAVuXqU0lQoR9rBIy968=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 06:01:30 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "k9n1kkbua6"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.css.map.gz"}, {"Name": "integrity", "Value": "sha256-HBgFcfSZJMq1nxRljAc5eNlMAVuXqU0lQoR9rBIy968="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.css.map", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\kwpabttjim-k9n1kkbua6.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000010200543"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "98033"}, {"Name": "ETag", "Value": "\"HBgFcfSZJMq1nxRljAc5eNlMAVuXqU0lQoR9rBIy968=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 06:01:30 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"CMAZj3JKoZUVYSxVPS/FeGatmYSvHkujo5oaZLNXx0o=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-CMAZj3JKoZUVYSxVPS/FeGatmYSvHkujo5oaZLNXx0o="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.css.map", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "492048"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"CMAZj3JKoZUVYSxVPS/FeGatmYSvHkujo5oaZLNXx0o=\""}, {"Name": "Last-Modified", "Value": "Wed, 14 May 2025 14:28:06 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-CMAZj3JKoZUVYSxVPS/FeGatmYSvHkujo5oaZLNXx0o="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.css.map.gz", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\kwpabttjim-k9n1kkbua6.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "98033"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"HBgFcfSZJMq1nxRljAc5eNlMAVuXqU0lQoR9rBIy968=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 06:01:30 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-HBgFcfSZJMq1nxRljAc5eNlMAVuXqU0lQoR9rBIy968="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.gawgt6fljy.css", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\xl2utgi5t6-gawgt6fljy.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000038839476"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "25746"}, {"Name": "ETag", "Value": "\"LgKX0350Ug6XsDByPQMOQx1sihJrlzqNLL47LiyoSQ4=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 06:01:30 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"eEx7gvq+uEM0o4kUBiy/+Mxl6rHH9NQ9UzRBWHe9mXg=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "gawgt6fljy"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.css"}, {"Name": "integrity", "Value": "sha256-eEx7gvq+uEM0o4kUBiy/+Mxl6rHH9NQ9UzRBWHe9mXg="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.gawgt6fljy.css", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "202385"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"eEx7gvq+uEM0o4kUBiy/+Mxl6rHH9NQ9UzRBWHe9mXg=\""}, {"Name": "Last-Modified", "Value": "Wed, 14 May 2025 14:28:06 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "gawgt6fljy"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.css"}, {"Name": "integrity", "Value": "sha256-eEx7gvq+uEM0o4kUBiy/+Mxl6rHH9NQ9UzRBWHe9mXg="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.gawgt6fljy.css.gz", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\xl2utgi5t6-gawgt6fljy.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "25746"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"LgKX0350Ug6XsDByPQMOQx1sihJrlzqNLL47LiyoSQ4=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 06:01:30 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "gawgt6fljy"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.css.gz"}, {"Name": "integrity", "Value": "sha256-LgKX0350Ug6XsDByPQMOQx1sihJrlzqNLL47LiyoSQ4="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.min.css", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\0kpipdbxfq-etnb7xlipe.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000043023706"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "23242"}, {"Name": "ETag", "Value": "\"T2rPGX2rORFBp2vX1wlC4q43KPqCjKbVkY+VHIDgmP0=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 06:01:30 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"rldnE7wZYJj3Q43t5v8fg1ojKRwyt0Wtfm+224CacZs=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-rldnE7wZYJj3Q43t5v8fg1ojKRwyt0Wtfm+224CacZs="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.min.css", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "155764"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"rldnE7wZYJj3Q43t5v8fg1ojKRwyt0Wtfm+224CacZs=\""}, {"Name": "Last-Modified", "Value": "Wed, 14 May 2025 14:28:06 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-rldnE7wZYJj3Q43t5v8fg1ojKRwyt0Wtfm+224CacZs="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.min.css.gz", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\0kpipdbxfq-etnb7xlipe.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "23242"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"T2rPGX2rORFBp2vX1wlC4q43KPqCjKbVkY+VHIDgmP0=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 06:01:30 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-T2rPGX2rORFBp2vX1wlC4q43KPqCjKbVkY+VHIDgmP0="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.min.css.kao5znno1s.map", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\3l64p4di12-kao5znno1s.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000009797198"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "102069"}, {"Name": "ETag", "Value": "\"8SkbHnWWeJ8KG1+bQUkOaPN31v/xtm4ZycUWB/SvS20=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 06:01:30 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"xMZ0SaSBYZSHVjFdZTAT/IjRExRIxSriWcJLcA9nkj0=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "kao5znno1s"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.min.css.map"}, {"Name": "integrity", "Value": "sha256-xMZ0SaSBYZSHVjFdZTAT/IjRExRIxSriWcJLcA9nkj0="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.min.css.kao5znno1s.map", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "625953"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"xMZ0SaSBYZSHVjFdZTAT/IjRExRIxSriWcJLcA9nkj0=\""}, {"Name": "Last-Modified", "Value": "Wed, 14 May 2025 14:28:06 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "kao5znno1s"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.min.css.map"}, {"Name": "integrity", "Value": "sha256-xMZ0SaSBYZSHVjFdZTAT/IjRExRIxSriWcJLcA9nkj0="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.min.css.kao5znno1s.map.gz", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\3l64p4di12-kao5znno1s.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "102069"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"8SkbHnWWeJ8KG1+bQUkOaPN31v/xtm4ZycUWB/SvS20=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 06:01:30 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "kao5znno1s"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.min.css.map.gz"}, {"Name": "integrity", "Value": "sha256-8SkbHnWWeJ8KG1+bQUkOaPN31v/xtm4ZycUWB/SvS20="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.min.css.map", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\3l64p4di12-kao5znno1s.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000009797198"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "102069"}, {"Name": "ETag", "Value": "\"8SkbHnWWeJ8KG1+bQUkOaPN31v/xtm4ZycUWB/SvS20=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 06:01:30 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"xMZ0SaSBYZSHVjFdZTAT/IjRExRIxSriWcJLcA9nkj0=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-xMZ0SaSBYZSHVjFdZTAT/IjRExRIxSriWcJLcA9nkj0="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.min.css.map", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "625953"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"xMZ0SaSBYZSHVjFdZTAT/IjRExRIxSriWcJLcA9nkj0=\""}, {"Name": "Last-Modified", "Value": "Wed, 14 May 2025 14:28:06 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-xMZ0SaSBYZSHVjFdZTAT/IjRExRIxSriWcJLcA9nkj0="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.min.css.map.gz", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\3l64p4di12-kao5znno1s.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "102069"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"8SkbHnWWeJ8KG1+bQUkOaPN31v/xtm4ZycUWB/SvS20=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 06:01:30 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-8SkbHnWWeJ8KG1+bQUkOaPN31v/xtm4ZycUWB/SvS20="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.min.etnb7xlipe.css", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\0kpipdbxfq-etnb7xlipe.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000043023706"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "23242"}, {"Name": "ETag", "Value": "\"T2rPGX2rORFBp2vX1wlC4q43KPqCjKbVkY+VHIDgmP0=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 06:01:30 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"rldnE7wZYJj3Q43t5v8fg1ojKRwyt0Wtfm+224CacZs=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "etnb7xlipe"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.min.css"}, {"Name": "integrity", "Value": "sha256-rldnE7wZYJj3Q43t5v8fg1ojKRwyt0Wtfm+224CacZs="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.min.etnb7xlipe.css", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "155764"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"rldnE7wZYJj3Q43t5v8fg1ojKRwyt0Wtfm+224CacZs=\""}, {"Name": "Last-Modified", "Value": "Wed, 14 May 2025 14:28:06 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "etnb7xlipe"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.min.css"}, {"Name": "integrity", "Value": "sha256-rldnE7wZYJj3Q43t5v8fg1ojKRwyt0Wtfm+224CacZs="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.min.etnb7xlipe.css.gz", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\0kpipdbxfq-etnb7xlipe.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "23242"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"T2rPGX2rORFBp2vX1wlC4q43KPqCjKbVkY+VHIDgmP0=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 06:01:30 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "etnb7xlipe"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.min.css.gz"}, {"Name": "integrity", "Value": "sha256-T2rPGX2rORFBp2vX1wlC4q43KPqCjKbVkY+VHIDgmP0="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.9n0ta5ieki.css", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\dunpfset9o-9n0ta5ieki.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000143533802"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "6966"}, {"Name": "ETag", "Value": "\"ZY60kbNkADk4KDRhW+LL+izegJBs9qdfvXorvDd7lIc=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 06:01:30 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"ezlzqfTqJZTjEDR18neKc2tUTZtWoz3oQ/ouWY9WmO8=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "9n0ta5ieki"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.css"}, {"Name": "integrity", "Value": "sha256-ezlzqfTqJZTjEDR18neKc2tUTZtWoz3oQ/ouWY9WmO8="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.9n0ta5ieki.css", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "68266"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"ezlzqfTqJZTjEDR18neKc2tUTZtWoz3oQ/ouWY9WmO8=\""}, {"Name": "Last-Modified", "Value": "Wed, 14 May 2025 14:28:06 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "9n0ta5ieki"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.css"}, {"Name": "integrity", "Value": "sha256-ezlzqfTqJZTjEDR18neKc2tUTZtWoz3oQ/ouWY9WmO8="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.9n0ta5ieki.css.gz", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\dunpfset9o-9n0ta5ieki.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "6966"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"ZY60kbNkADk4KDRhW+LL+izegJBs9qdfvXorvDd7lIc=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 06:01:30 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "9n0ta5ieki"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.css.gz"}, {"Name": "integrity", "Value": "sha256-ZY60kbNkADk4KDRhW+LL+izegJBs9qdfvXorvDd7lIc="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.css", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\dunpfset9o-9n0ta5ieki.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000143533802"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "6966"}, {"Name": "ETag", "Value": "\"ZY60kbNkADk4KDRhW+LL+izegJBs9qdfvXorvDd7lIc=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 06:01:30 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"ezlzqfTqJZTjEDR18neKc2tUTZtWoz3oQ/ouWY9WmO8=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ezlzqfTqJZTjEDR18neKc2tUTZtWoz3oQ/ouWY9WmO8="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.css", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "68266"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"ezlzqfTqJZTjEDR18neKc2tUTZtWoz3oQ/ouWY9WmO8=\""}, {"Name": "Last-Modified", "Value": "Wed, 14 May 2025 14:28:06 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ezlzqfTqJZTjEDR18neKc2tUTZtWoz3oQ/ouWY9WmO8="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.css.0i1dcxd824.map", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\6lzqm95o0o-0i1dcxd824.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000036950818"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "27062"}, {"Name": "ETag", "Value": "\"oytvk6SQ3cnlPxAF33gxI7GS06Rj2MrRoUPoiaFchjQ=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 06:01:30 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"2MgjO0zpqYZscatQSGWJ9Io9U8EjvXk0iYygYz1Q+Ms=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "0i1dcxd824"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.css.map"}, {"Name": "integrity", "Value": "sha256-2MgjO0zpqYZscatQSGWJ9Io9U8EjvXk0iYygYz1Q+Ms="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.css.0i1dcxd824.map", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "151749"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"2MgjO0zpqYZscatQSGWJ9Io9U8EjvXk0iYygYz1Q+Ms=\""}, {"Name": "Last-Modified", "Value": "Wed, 14 May 2025 14:28:06 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "0i1dcxd824"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.css.map"}, {"Name": "integrity", "Value": "sha256-2MgjO0zpqYZscatQSGWJ9Io9U8EjvXk0iYygYz1Q+Ms="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.css.0i1dcxd824.map.gz", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\6lzqm95o0o-0i1dcxd824.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "27062"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"oytvk6SQ3cnlPxAF33gxI7GS06Rj2MrRoUPoiaFchjQ=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 06:01:30 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "0i1dcxd824"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.css.map.gz"}, {"Name": "integrity", "Value": "sha256-oytvk6SQ3cnlPxAF33gxI7GS06Rj2MrRoUPoiaFchjQ="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.css.gz", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\dunpfset9o-9n0ta5ieki.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "6966"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"ZY60kbNkADk4KDRhW+LL+izegJBs9qdfvXorvDd7lIc=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 06:01:30 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ZY60kbNkADk4KDRhW+LL+izegJBs9qdfvXorvDd7lIc="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.css.map", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\6lzqm95o0o-0i1dcxd824.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000036950818"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "27062"}, {"Name": "ETag", "Value": "\"oytvk6SQ3cnlPxAF33gxI7GS06Rj2MrRoUPoiaFchjQ=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 06:01:30 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"2MgjO0zpqYZscatQSGWJ9Io9U8EjvXk0iYygYz1Q+Ms=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-2MgjO0zpqYZscatQSGWJ9Io9U8EjvXk0iYygYz1Q+Ms="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.css.map", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "151749"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"2MgjO0zpqYZscatQSGWJ9Io9U8EjvXk0iYygYz1Q+Ms=\""}, {"Name": "Last-Modified", "Value": "Wed, 14 May 2025 14:28:06 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-2MgjO0zpqYZscatQSGWJ9Io9U8EjvXk0iYygYz1Q+Ms="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.css.map.gz", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\6lzqm95o0o-0i1dcxd824.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "27062"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"oytvk6SQ3cnlPxAF33gxI7GS06Rj2MrRoUPoiaFchjQ=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 06:01:30 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-oytvk6SQ3cnlPxAF33gxI7GS06Rj2MrRoUPoiaFchjQ="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.min.css", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\gcqjvrmgyi-vxs71z90fw.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000165837479"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "6029"}, {"Name": "ETag", "Value": "\"HFjfcB/gV8FeYhFO87HnfGGK/Ebdg885i044QcdVncY=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 06:01:30 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"vdSFRAWr6LTognRmxyi6QlSO5O+MC+VGyMbziTrBmBQ=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-vdSFRAWr6LTognRmxyi6QlSO5O+MC+VGyMbziTrBmBQ="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.min.css", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "48494"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"vdSFRAWr6LTognRmxyi6QlSO5O+MC+VGyMbziTrBmBQ=\""}, {"Name": "Last-Modified", "Value": "Wed, 14 May 2025 14:28:06 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-vdSFRAWr6LTognRmxyi6QlSO5O+MC+VGyMbziTrBmBQ="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.min.css.gz", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\gcqjvrmgyi-vxs71z90fw.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "6029"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"HFjfcB/gV8FeYhFO87HnfGGK/Ebdg885i044QcdVncY=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 06:01:30 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-HFjfcB/gV8FeYhFO87HnfGGK/Ebdg885i044QcdVncY="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.min.css.jzb7jyrjvs.map", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\ae6gh6t3i1-jzb7jyrjvs.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000071756602"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "13935"}, {"Name": "ETag", "Value": "\"ukFugDNptsdF2I0tTFVp+zrq3vCFvErB7PNxY2Pmf7s=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 06:01:30 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"kZzlXLpTC0WvL0AL7nXf07BJ28WnY/1H/HrKgS68Q4I=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "jzb7jyrjvs"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.min.css.map"}, {"Name": "integrity", "Value": "sha256-kZzlXLpTC0WvL0AL7nXf07BJ28WnY/1H/HrKgS68Q4I="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.min.css.jzb7jyrjvs.map", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "108539"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"kZzlXLpTC0WvL0AL7nXf07BJ28WnY/1H/HrKgS68Q4I=\""}, {"Name": "Last-Modified", "Value": "Wed, 14 May 2025 14:28:06 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "jzb7jyrjvs"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.min.css.map"}, {"Name": "integrity", "Value": "sha256-kZzlXLpTC0WvL0AL7nXf07BJ28WnY/1H/HrKgS68Q4I="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.min.css.jzb7jyrjvs.map.gz", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\ae6gh6t3i1-jzb7jyrjvs.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "13935"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"ukFugDNptsdF2I0tTFVp+zrq3vCFvErB7PNxY2Pmf7s=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 06:01:30 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "jzb7jyrjvs"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.min.css.map.gz"}, {"Name": "integrity", "Value": "sha256-ukFugDNptsdF2I0tTFVp+zrq3vCFvErB7PNxY2Pmf7s="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.min.css.map", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\ae6gh6t3i1-jzb7jyrjvs.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000071756602"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "13935"}, {"Name": "ETag", "Value": "\"ukFugDNptsdF2I0tTFVp+zrq3vCFvErB7PNxY2Pmf7s=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 06:01:30 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"kZzlXLpTC0WvL0AL7nXf07BJ28WnY/1H/HrKgS68Q4I=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-kZzlXLpTC0WvL0AL7nXf07BJ28WnY/1H/HrKgS68Q4I="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.min.css.map", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "108539"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"kZzlXLpTC0WvL0AL7nXf07BJ28WnY/1H/HrKgS68Q4I=\""}, {"Name": "Last-Modified", "Value": "Wed, 14 May 2025 14:28:06 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-kZzlXLpTC0WvL0AL7nXf07BJ28WnY/1H/HrKgS68Q4I="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.min.css.map.gz", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\ae6gh6t3i1-jzb7jyrjvs.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "13935"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"ukFugDNptsdF2I0tTFVp+zrq3vCFvErB7PNxY2Pmf7s=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 06:01:30 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ukFugDNptsdF2I0tTFVp+zrq3vCFvErB7PNxY2Pmf7s="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.min.vxs71z90fw.css", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\gcqjvrmgyi-vxs71z90fw.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000165837479"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "6029"}, {"Name": "ETag", "Value": "\"HFjfcB/gV8FeYhFO87HnfGGK/Ebdg885i044QcdVncY=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 06:01:30 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"vdSFRAWr6LTognRmxyi6QlSO5O+MC+VGyMbziTrBmBQ=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "vxs71z90fw"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.min.css"}, {"Name": "integrity", "Value": "sha256-vdSFRAWr6LTognRmxyi6QlSO5O+MC+VGyMbziTrBmBQ="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.min.vxs71z90fw.css", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "48494"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"vdSFRAWr6LTognRmxyi6QlSO5O+MC+VGyMbziTrBmBQ=\""}, {"Name": "Last-Modified", "Value": "Wed, 14 May 2025 14:28:06 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "vxs71z90fw"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.min.css"}, {"Name": "integrity", "Value": "sha256-vdSFRAWr6LTognRmxyi6QlSO5O+MC+VGyMbziTrBmBQ="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.min.vxs71z90fw.css.gz", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\gcqjvrmgyi-vxs71z90fw.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "6029"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"HFjfcB/gV8FeYhFO87HnfGGK/Ebdg885i044QcdVncY=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 06:01:30 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "vxs71z90fw"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.min.css.gz"}, {"Name": "integrity", "Value": "sha256-HFjfcB/gV8FeYhFO87HnfGGK/Ebdg885i044QcdVncY="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.css", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\0dvwnkaaa0-nynt4yc5xr.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000575373993"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1737"}, {"Name": "ETag", "Value": "\"ski9h0PufafI6hMaaPSRPSY+iMBc1VbPoK1FYjUXHMQ=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 06:01:30 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"cZWhU8ntBevnbhNEh3hunnIoi6dE1eUwX41sB6+bHmo=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-cZWhU8ntBevnbhNEh3hunnIoi6dE1eUwX41sB6+bHmo="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.css", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5227"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"cZWhU8ntBevnbhNEh3hunnIoi6dE1eUwX41sB6+bHmo=\""}, {"Name": "Last-Modified", "Value": "Wed, 14 May 2025 14:28:06 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-cZWhU8ntBevnbhNEh3hunnIoi6dE1eUwX41sB6+bHmo="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.css.gz", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\0dvwnkaaa0-nynt4yc5xr.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1737"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"ski9h0PufafI6hMaaPSRPSY+iMBc1VbPoK1FYjUXHMQ=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 06:01:30 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ski9h0PufafI6hMaaPSRPSY+iMBc1VbPoK1FYjUXHMQ="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.css.map", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\ye5ch5e0ju-tgg2bl5mrw.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000058795861"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "17007"}, {"Name": "ETag", "Value": "\"PgWj9lwXprPCKRJlELXHPizyFeevIvOzAMOrcCGtBdI=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 06:01:30 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"3e6awqXPijx918dzyzbbHKwaDBTsIQ8Us38QY30GRms=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-3e6awqXPijx918dzyzbbHKwaDBTsIQ8Us38QY30GRms="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.css.map", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "76483"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"3e6awqXPijx918dzyzbbHKwaDBTsIQ8Us38QY30GRms=\""}, {"Name": "Last-Modified", "Value": "Wed, 14 May 2025 14:28:06 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-3e6awqXPijx918dzyzbbHKwaDBTsIQ8Us38QY30GRms="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.css.map.gz", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\ye5ch5e0ju-tgg2bl5mrw.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "17007"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"PgWj9lwXprPCKRJlELXHPizyFeevIvOzAMOrcCGtBdI=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 06:01:30 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-PgWj9lwXprPCKRJlELXHPizyFeevIvOzAMOrcCGtBdI="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.css.tgg2bl5mrw.map", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\ye5ch5e0ju-tgg2bl5mrw.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000058795861"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "17007"}, {"Name": "ETag", "Value": "\"PgWj9lwXprPCKRJlELXHPizyFeevIvOzAMOrcCGtBdI=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 06:01:30 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"3e6awqXPijx918dzyzbbHKwaDBTsIQ8Us38QY30GRms=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "tgg2bl5mrw"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.css.map"}, {"Name": "integrity", "Value": "sha256-3e6awqXPijx918dzyzbbHKwaDBTsIQ8Us38QY30GRms="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.css.tgg2bl5mrw.map", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "76483"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"3e6awqXPijx918dzyzbbHKwaDBTsIQ8Us38QY30GRms=\""}, {"Name": "Last-Modified", "Value": "Wed, 14 May 2025 14:28:06 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "tgg2bl5mrw"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.css.map"}, {"Name": "integrity", "Value": "sha256-3e6awqXPijx918dzyzbbHKwaDBTsIQ8Us38QY30GRms="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.css.tgg2bl5mrw.map.gz", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\ye5ch5e0ju-tgg2bl5mrw.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "17007"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"PgWj9lwXprPCKRJlELXHPizyFeevIvOzAMOrcCGtBdI=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 06:01:30 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "tgg2bl5mrw"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.css.map.gz"}, {"Name": "integrity", "Value": "sha256-PgWj9lwXprPCKRJlELXHPizyFeevIvOzAMOrcCGtBdI="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.min.brwg1hntyu.css", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\odolngyg4q-brwg1hntyu.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000618811881"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1615"}, {"Name": "ETag", "Value": "\"SK/uC88mzOjgHwihIpIpaGtZcvPkQkQKkO0iusPs+Z0=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 06:01:30 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"1z7sRzwNyqpcTrqPd/iXuzoApqDdjpRbHwjAZ8QMhOk=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "brwg1hntyu"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.min.css"}, {"Name": "integrity", "Value": "sha256-1z7sRzwNyqpcTrqPd/iXuzoApqDdjpRbHwjAZ8QMhOk="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.min.brwg1hntyu.css", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "4028"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"1z7sRzwNyqpcTrqPd/iXuzoApqDdjpRbHwjAZ8QMhOk=\""}, {"Name": "Last-Modified", "Value": "Wed, 14 May 2025 14:28:06 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "brwg1hntyu"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.min.css"}, {"Name": "integrity", "Value": "sha256-1z7sRzwNyqpcTrqPd/iXuzoApqDdjpRbHwjAZ8QMhOk="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.min.brwg1hntyu.css.gz", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\odolngyg4q-brwg1hntyu.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1615"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"SK/uC88mzOjgHwihIpIpaGtZcvPkQkQKkO0iusPs+Z0=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 06:01:30 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "brwg1hntyu"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.min.css.gz"}, {"Name": "integrity", "Value": "sha256-SK/uC88mzOjgHwihIpIpaGtZcvPkQkQKkO0iusPs+Z0="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.min.css", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\odolngyg4q-brwg1hntyu.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000618811881"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1615"}, {"Name": "ETag", "Value": "\"SK/uC88mzOjgHwihIpIpaGtZcvPkQkQKkO0iusPs+Z0=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 06:01:30 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"1z7sRzwNyqpcTrqPd/iXuzoApqDdjpRbHwjAZ8QMhOk=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-1z7sRzwNyqpcTrqPd/iXuzoApqDdjpRbHwjAZ8QMhOk="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.min.css", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "4028"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"1z7sRzwNyqpcTrqPd/iXuzoApqDdjpRbHwjAZ8QMhOk=\""}, {"Name": "Last-Modified", "Value": "Wed, 14 May 2025 14:28:06 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-1z7sRzwNyqpcTrqPd/iXuzoApqDdjpRbHwjAZ8QMhOk="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.min.css.gf2dxac9qe.map", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\s7wjcvroec-gf2dxac9qe.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000124378109"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "8039"}, {"Name": "ETag", "Value": "\"+vu0DGLWqkGPTrKaf7U22OpO4XImAF0LW8HvFHEnCKA=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 06:01:30 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"dIm3VZXztwbIlhOzVt+ggg5Dvhp28MJQGJoweOH9cAE=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "gf2dxac9qe"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.min.css.map"}, {"Name": "integrity", "Value": "sha256-dIm3VZXztwbIlhOzVt+ggg5Dvhp28MJQGJoweOH9cAE="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.min.css.gf2dxac9qe.map", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "32461"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"dIm3VZXztwbIlhOzVt+ggg5Dvhp28MJQGJoweOH9cAE=\""}, {"Name": "Last-Modified", "Value": "Wed, 14 May 2025 14:28:06 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "gf2dxac9qe"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.min.css.map"}, {"Name": "integrity", "Value": "sha256-dIm3VZXztwbIlhOzVt+ggg5Dvhp28MJQGJoweOH9cAE="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.min.css.gf2dxac9qe.map.gz", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\s7wjcvroec-gf2dxac9qe.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "8039"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"+vu0DGLWqkGPTrKaf7U22OpO4XImAF0LW8HvFHEnCKA=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 06:01:30 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "gf2dxac9qe"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.min.css.map.gz"}, {"Name": "integrity", "Value": "sha256-+vu0DGLWqkGPTrKaf7U22OpO4XImAF0LW8HvFHEnCKA="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.min.css.gz", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\odolngyg4q-brwg1hntyu.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1615"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"SK/uC88mzOjgHwihIpIpaGtZcvPkQkQKkO0iusPs+Z0=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 06:01:30 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-SK/uC88mzOjgHwihIpIpaGtZcvPkQkQKkO0iusPs+Z0="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.min.css.map", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\s7wjcvroec-gf2dxac9qe.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000124378109"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "8039"}, {"Name": "ETag", "Value": "\"+vu0DGLWqkGPTrKaf7U22OpO4XImAF0LW8HvFHEnCKA=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 06:01:30 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"dIm3VZXztwbIlhOzVt+ggg5Dvhp28MJQGJoweOH9cAE=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-dIm3VZXztwbIlhOzVt+ggg5Dvhp28MJQGJoweOH9cAE="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.min.css.map", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "32461"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"dIm3VZXztwbIlhOzVt+ggg5Dvhp28MJQGJoweOH9cAE=\""}, {"Name": "Last-Modified", "Value": "Wed, 14 May 2025 14:28:06 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-dIm3VZXztwbIlhOzVt+ggg5Dvhp28MJQGJoweOH9cAE="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.min.css.map.gz", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\s7wjcvroec-gf2dxac9qe.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "8039"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"+vu0DGLWqkGPTrKaf7U22OpO4XImAF0LW8HvFHEnCKA=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 06:01:30 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-+vu0DGLWqkGPTrKaf7U22OpO4XImAF0LW8HvFHEnCKA="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.nynt4yc5xr.css", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\0dvwnkaaa0-nynt4yc5xr.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000575373993"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1737"}, {"Name": "ETag", "Value": "\"ski9h0PufafI6hMaaPSRPSY+iMBc1VbPoK1FYjUXHMQ=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 06:01:30 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"cZWhU8ntBevnbhNEh3hunnIoi6dE1eUwX41sB6+bHmo=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "nynt4yc5xr"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.css"}, {"Name": "integrity", "Value": "sha256-cZWhU8ntBevnbhNEh3hunnIoi6dE1eUwX41sB6+bHmo="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.nynt4yc5xr.css", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5227"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"cZWhU8ntBevnbhNEh3hunnIoi6dE1eUwX41sB6+bHmo=\""}, {"Name": "Last-Modified", "Value": "Wed, 14 May 2025 14:28:06 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "nynt4yc5xr"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.css"}, {"Name": "integrity", "Value": "sha256-cZWhU8ntBevnbhNEh3hunnIoi6dE1eUwX41sB6+bHmo="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.nynt4yc5xr.css.gz", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\0dvwnkaaa0-nynt4yc5xr.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1737"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"ski9h0PufafI6hMaaPSRPSY+iMBc1VbPoK1FYjUXHMQ=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 06:01:30 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "nynt4yc5xr"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.css.gz"}, {"Name": "integrity", "Value": "sha256-ski9h0PufafI6hMaaPSRPSY+iMBc1VbPoK1FYjUXHMQ="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.48vr37mrsy.js", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\s7f8b8w26r-48vr37mrsy.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000039550704"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "25283"}, {"Name": "ETag", "Value": "\"1Ct5pGkD+FF0LlXrT4xzvGbCR79KcfwrOutx4z65BIc=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 06:01:30 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"LKpkBN2w3iudGRseLItcNcaMpI8qlSEUC7+DsnwGNwA=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "48vr37mrsy"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.js"}, {"Name": "integrity", "Value": "sha256-LKpkBN2w3iudGRseLItcNcaMpI8qlSEUC7+DsnwGNwA="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.48vr37mrsy.js", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "136072"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"LKpkBN2w3iudGRseLItcNcaMpI8qlSEUC7+DsnwGNwA=\""}, {"Name": "Last-Modified", "Value": "Wed, 14 May 2025 14:28:06 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "48vr37mrsy"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.js"}, {"Name": "integrity", "Value": "sha256-LKpkBN2w3iudGRseLItcNcaMpI8qlSEUC7+DsnwGNwA="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.48vr37mrsy.js.gz", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\s7f8b8w26r-48vr37mrsy.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "25283"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"1Ct5pGkD+FF0LlXrT4xzvGbCR79KcfwrOutx4z65BIc=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 06:01:30 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "48vr37mrsy"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.js.gz"}, {"Name": "integrity", "Value": "sha256-1Ct5pGkD+FF0LlXrT4xzvGbCR79KcfwrOutx4z65BIc="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.js", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\cbjq4inumo-u0biprgly9.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000020684234"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "48345"}, {"Name": "ETag", "Value": "\"KcZfC8VlRGpzBSf8kYjbjjvxZ1mENKljqzW5X1VIqCY=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 06:01:30 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"srIwGYgANrjaabGVuC3G7O0jv1Xh3Kt7dIc3/P0Ebf0=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-srIwGYgANrjaabGVuC3G7O0jv1Xh3Kt7dIc3/P0Ebf0="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.js", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "229924"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"srIwGYgANrjaabGVuC3G7O0jv1Xh3Kt7dIc3/P0Ebf0=\""}, {"Name": "Last-Modified", "Value": "Wed, 14 May 2025 14:28:06 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-srIwGYgANrjaabGVuC3G7O0jv1Xh3Kt7dIc3/P0Ebf0="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.js.gz", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\cbjq4inumo-u0biprgly9.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "48345"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"KcZfC8VlRGpzBSf8kYjbjjvxZ1mENKljqzW5X1VIqCY=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 06:01:30 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-KcZfC8VlRGpzBSf8kYjbjjvxZ1mENKljqzW5X1VIqCY="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.js.map", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\cip0knvmfd-pxamm17y9e.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000010933622"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "91460"}, {"Name": "ETag", "Value": "\"IyKBFJ2NdYfuuICMUQxeCcGk/bTVfEdW5iAvM9nw0oU=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 06:01:30 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"3UpdqvoTc6M2sug8WtFhr/m3tg+4zLMgoMgjqpn5n1I=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-3UpdqvoTc6M2sug8WtFhr/m3tg+4zLMgoMgjqpn5n1I="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.js.map", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "402249"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"3UpdqvoTc6M2sug8WtFhr/m3tg+4zLMgoMgjqpn5n1I=\""}, {"Name": "Last-Modified", "Value": "Wed, 14 May 2025 14:28:06 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-3UpdqvoTc6M2sug8WtFhr/m3tg+4zLMgoMgjqpn5n1I="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.js.map.gz", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\cip0knvmfd-pxamm17y9e.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "91460"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"IyKBFJ2NdYfuuICMUQxeCcGk/bTVfEdW5iAvM9nw0oU=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 06:01:30 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-IyKBFJ2NdYfuuICMUQxeCcGk/bTVfEdW5iAvM9nw0oU="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.js.pxamm17y9e.map", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\cip0knvmfd-pxamm17y9e.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000010933622"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "91460"}, {"Name": "ETag", "Value": "\"IyKBFJ2NdYfuuICMUQxeCcGk/bTVfEdW5iAvM9nw0oU=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 06:01:30 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"3UpdqvoTc6M2sug8WtFhr/m3tg+4zLMgoMgjqpn5n1I=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "pxamm17y9e"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.bundle.js.map"}, {"Name": "integrity", "Value": "sha256-3UpdqvoTc6M2sug8WtFhr/m3tg+4zLMgoMgjqpn5n1I="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.js.pxamm17y9e.map", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "402249"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"3UpdqvoTc6M2sug8WtFhr/m3tg+4zLMgoMgjqpn5n1I=\""}, {"Name": "Last-Modified", "Value": "Wed, 14 May 2025 14:28:06 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "pxamm17y9e"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.bundle.js.map"}, {"Name": "integrity", "Value": "sha256-3UpdqvoTc6M2sug8WtFhr/m3tg+4zLMgoMgjqpn5n1I="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.js.pxamm17y9e.map.gz", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\cip0knvmfd-pxamm17y9e.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "91460"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"IyKBFJ2NdYfuuICMUQxeCcGk/bTVfEdW5iAvM9nw0oU=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 06:01:30 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "pxamm17y9e"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.bundle.js.map.gz"}, {"Name": "integrity", "Value": "sha256-IyKBFJ2NdYfuuICMUQxeCcGk/bTVfEdW5iAvM9nw0oU="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.min.hjuzisly30.js", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\zo36bj6qtt-hjuzisly30.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000044845060"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "22298"}, {"Name": "ETag", "Value": "\"1MiwfybWPUf85W8WHsUjrjh6SWZrvKae/f829+FAkhU=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 06:01:30 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"XZfkOGd6FuhF88h5GgEmRIpXbm+hBkFo74yYDPY5rbw=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "hju<PERSON><PERSON>ly30"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.bundle.min.js"}, {"Name": "integrity", "Value": "sha256-XZfkOGd6FuhF88h5GgEmRIpXbm+hBkFo74yYDPY5rbw="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.min.hjuzisly30.js", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "78641"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"XZfkOGd6FuhF88h5GgEmRIpXbm+hBkFo74yYDPY5rbw=\""}, {"Name": "Last-Modified", "Value": "Wed, 14 May 2025 14:28:06 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "hju<PERSON><PERSON>ly30"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.bundle.min.js"}, {"Name": "integrity", "Value": "sha256-XZfkOGd6FuhF88h5GgEmRIpXbm+hBkFo74yYDPY5rbw="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.min.hjuzisly30.js.gz", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\zo36bj6qtt-hjuzisly30.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "22298"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"1MiwfybWPUf85W8WHsUjrjh6SWZrvKae/f829+FAkhU=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 06:01:30 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "hju<PERSON><PERSON>ly30"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.bundle.min.js.gz"}, {"Name": "integrity", "Value": "sha256-1MiwfybWPUf85W8WHsUjrjh6SWZrvKae/f829+FAkhU="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.min.js", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\zo36bj6qtt-hjuzisly30.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000044845060"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "22298"}, {"Name": "ETag", "Value": "\"1MiwfybWPUf85W8WHsUjrjh6SWZrvKae/f829+FAkhU=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 06:01:30 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"XZfkOGd6FuhF88h5GgEmRIpXbm+hBkFo74yYDPY5rbw=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-XZfkOGd6FuhF88h5GgEmRIpXbm+hBkFo74yYDPY5rbw="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.min.js", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "78641"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"XZfkOGd6FuhF88h5GgEmRIpXbm+hBkFo74yYDPY5rbw=\""}, {"Name": "Last-Modified", "Value": "Wed, 14 May 2025 14:28:06 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-XZfkOGd6FuhF88h5GgEmRIpXbm+hBkFo74yYDPY5rbw="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.min.js.gz", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\zo36bj6qtt-hjuzisly30.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "22298"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"1MiwfybWPUf85W8WHsUjrjh6SWZrvKae/f829+FAkhU=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 06:01:30 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-1MiwfybWPUf85W8WHsUjrjh6SWZrvKae/f829+FAkhU="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.min.js.map", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\ba2jr6qd4r-u9xms436mi.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000012188879"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "82041"}, {"Name": "ETag", "Value": "\"BHUyrJ6l5FX9FTIykDazdjlP73jzpfplry3r+6zSdoE=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 06:01:30 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"8i3JQdKYQQcJzmbkwhwY+1XPe7Utf1LdBnYZCvNmKWc=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-8i3JQdKYQQcJzmbkwhwY+1XPe7Utf1LdBnYZCvNmKWc="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.min.js.map", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "311949"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"8i3JQdKYQQcJzmbkwhwY+1XPe7Utf1LdBnYZCvNmKWc=\""}, {"Name": "Last-Modified", "Value": "Wed, 14 May 2025 14:28:06 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-8i3JQdKYQQcJzmbkwhwY+1XPe7Utf1LdBnYZCvNmKWc="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.min.js.map.gz", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\ba2jr6qd4r-u9xms436mi.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "82041"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"BHUyrJ6l5FX9FTIykDazdjlP73jzpfplry3r+6zSdoE=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 06:01:30 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-BH<PERSON>yrJ6l5FX9FTIykDazdjlP73jzpfplry3r+6zSdoE="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.min.js.u9xms436mi.map", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\ba2jr6qd4r-u9xms436mi.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000012188879"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "82041"}, {"Name": "ETag", "Value": "\"BHUyrJ6l5FX9FTIykDazdjlP73jzpfplry3r+6zSdoE=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 06:01:30 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"8i3JQdKYQQcJzmbkwhwY+1XPe7Utf1LdBnYZCvNmKWc=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "u9xms436mi"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.bundle.min.js.map"}, {"Name": "integrity", "Value": "sha256-8i3JQdKYQQcJzmbkwhwY+1XPe7Utf1LdBnYZCvNmKWc="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.min.js.u9xms436mi.map", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "311949"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"8i3JQdKYQQcJzmbkwhwY+1XPe7Utf1LdBnYZCvNmKWc=\""}, {"Name": "Last-Modified", "Value": "Wed, 14 May 2025 14:28:06 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "u9xms436mi"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.bundle.min.js.map"}, {"Name": "integrity", "Value": "sha256-8i3JQdKYQQcJzmbkwhwY+1XPe7Utf1LdBnYZCvNmKWc="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.min.js.u9xms436mi.map.gz", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\ba2jr6qd4r-u9xms436mi.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "82041"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"BHUyrJ6l5FX9FTIykDazdjlP73jzpfplry3r+6zSdoE=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 06:01:30 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "u9xms436mi"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.bundle.min.js.map.gz"}, {"Name": "integrity", "Value": "sha256-BH<PERSON>yrJ6l5FX9FTIykDazdjlP73jzpfplry3r+6zSdoE="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.u0biprgly9.js", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\cbjq4inumo-u0biprgly9.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000020684234"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "48345"}, {"Name": "ETag", "Value": "\"KcZfC8VlRGpzBSf8kYjbjjvxZ1mENKljqzW5X1VIqCY=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 06:01:30 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"srIwGYgANrjaabGVuC3G7O0jv1Xh3Kt7dIc3/P0Ebf0=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "u0biprgly9"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.bundle.js"}, {"Name": "integrity", "Value": "sha256-srIwGYgANrjaabGVuC3G7O0jv1Xh3Kt7dIc3/P0Ebf0="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.u0biprgly9.js", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "229924"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"srIwGYgANrjaabGVuC3G7O0jv1Xh3Kt7dIc3/P0Ebf0=\""}, {"Name": "Last-Modified", "Value": "Wed, 14 May 2025 14:28:06 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "u0biprgly9"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.bundle.js"}, {"Name": "integrity", "Value": "sha256-srIwGYgANrjaabGVuC3G7O0jv1Xh3Kt7dIc3/P0Ebf0="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.u0biprgly9.js.gz", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\cbjq4inumo-u0biprgly9.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "48345"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"KcZfC8VlRGpzBSf8kYjbjjvxZ1mENKljqzW5X1VIqCY=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 06:01:30 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "u0biprgly9"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.bundle.js.gz"}, {"Name": "integrity", "Value": "sha256-KcZfC8VlRGpzBSf8kYjbjjvxZ1mENKljqzW5X1VIqCY="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.js", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\s7f8b8w26r-48vr37mrsy.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000039550704"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "25283"}, {"Name": "ETag", "Value": "\"1Ct5pGkD+FF0LlXrT4xzvGbCR79KcfwrOutx4z65BIc=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 06:01:30 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"LKpkBN2w3iudGRseLItcNcaMpI8qlSEUC7+DsnwGNwA=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-LKpkBN2w3iudGRseLItcNcaMpI8qlSEUC7+DsnwGNwA="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.js", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "136072"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"LKpkBN2w3iudGRseLItcNcaMpI8qlSEUC7+DsnwGNwA=\""}, {"Name": "Last-Modified", "Value": "Wed, 14 May 2025 14:28:06 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-LKpkBN2w3iudGRseLItcNcaMpI8qlSEUC7+DsnwGNwA="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.js.gz", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\s7f8b8w26r-48vr37mrsy.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "25283"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"1Ct5pGkD+FF0LlXrT4xzvGbCR79KcfwrOutx4z65BIc=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 06:01:30 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-1Ct5pGkD+FF0LlXrT4xzvGbCR79KcfwrOutx4z65BIc="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.js.map", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\lyme58ep3m-vaswmcjbo4.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000017304930"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "57786"}, {"Name": "ETag", "Value": "\"V2nzFYIGfzW4T0n5xREU4Xq27AL4FR+ObKGcPUp3R8I=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 06:01:30 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"R81NA2DWe8EPjZ2OUhieXYgvvXBnm78oMdeqOtSEr7c=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-R81NA2DWe8EPjZ2OUhieXYgvvXBnm78oMdeqOtSEr7c="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.js.map", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "250568"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"R81NA2DWe8EPjZ2OUhieXYgvvXBnm78oMdeqOtSEr7c=\""}, {"Name": "Last-Modified", "Value": "Wed, 14 May 2025 14:28:06 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-R81NA2DWe8EPjZ2OUhieXYgvvXBnm78oMdeqOtSEr7c="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.js.map.gz", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\lyme58ep3m-vaswmcjbo4.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "57786"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"V2nzFYIGfzW4T0n5xREU4Xq27AL4FR+ObKGcPUp3R8I=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 06:01:30 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-V2nzFYIGfzW4T0n5xREU4Xq27AL4FR+ObKGcPUp3R8I="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.js.vaswmcjbo4.map", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\lyme58ep3m-vaswmcjbo4.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000017304930"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "57786"}, {"Name": "ETag", "Value": "\"V2nzFYIGfzW4T0n5xREU4Xq27AL4FR+ObKGcPUp3R8I=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 06:01:30 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"R81NA2DWe8EPjZ2OUhieXYgvvXBnm78oMdeqOtSEr7c=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "vaswmcjbo4"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.js.map"}, {"Name": "integrity", "Value": "sha256-R81NA2DWe8EPjZ2OUhieXYgvvXBnm78oMdeqOtSEr7c="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.js.vaswmcjbo4.map", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "250568"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"R81NA2DWe8EPjZ2OUhieXYgvvXBnm78oMdeqOtSEr7c=\""}, {"Name": "Last-Modified", "Value": "Wed, 14 May 2025 14:28:06 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "vaswmcjbo4"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.js.map"}, {"Name": "integrity", "Value": "sha256-R81NA2DWe8EPjZ2OUhieXYgvvXBnm78oMdeqOtSEr7c="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.js.vaswmcjbo4.map.gz", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\lyme58ep3m-vaswmcjbo4.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "57786"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"V2nzFYIGfzW4T0n5xREU4Xq27AL4FR+ObKGcPUp3R8I=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 06:01:30 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "vaswmcjbo4"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.js.map.gz"}, {"Name": "integrity", "Value": "sha256-V2nzFYIGfzW4T0n5xREU4Xq27AL4FR+ObKGcPUp3R8I="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.min.js", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\448jultfmw-zu238p5lxg.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000064758451"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "15441"}, {"Name": "ETag", "Value": "\"/zGCumvrl91atAvEbBmyY+KKn65ToLyg+icn9FQP8xU=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 06:01:30 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"O82ALp93hJ58HpPIcnn7uwTUWUnSvnmwNWbOrN4psVg=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-O82ALp93hJ58HpPIcnn7uwTUWUnSvnmwNWbOrN4psVg="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.min.js", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "58078"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"O82ALp93hJ58HpPIcnn7uwTUWUnSvnmwNWbOrN4psVg=\""}, {"Name": "Last-Modified", "Value": "Wed, 14 May 2025 14:28:06 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-O82ALp93hJ58HpPIcnn7uwTUWUnSvnmwNWbOrN4psVg="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.min.js.gz", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\448jultfmw-zu238p5lxg.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "15441"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"/zGCumvrl91atAvEbBmyY+KKn65ToLyg+icn9FQP8xU=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 06:01:30 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-/zGCumvrl91atAvEbBmyY+KKn65ToLyg+icn9FQP8xU="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.min.js.map", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\9hizxlbrr4-sgi57dik4g.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000021685859"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "46112"}, {"Name": "ETag", "Value": "\"y/W+IS3pBW6YOxougP7451HTyQ/Q1y9J/D94i5Ds7u8=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 06:01:30 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"vMfBbEXmojM9AaHrIyKSo+20n5JM7KMyJkBCfL4pgL4=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-vMfBbEXmojM9AaHrIyKSo+20n5JM7KMyJkBCfL4pgL4="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.min.js.map", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "190253"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"vMfBbEXmojM9AaHrIyKSo+20n5JM7KMyJkBCfL4pgL4=\""}, {"Name": "Last-Modified", "Value": "Wed, 14 May 2025 14:28:06 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-vMfBbEXmojM9AaHrIyKSo+20n5JM7KMyJkBCfL4pgL4="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.min.js.map.gz", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\9hizxlbrr4-sgi57dik4g.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "46112"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"y/W+IS3pBW6YOxougP7451HTyQ/Q1y9J/D94i5Ds7u8=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 06:01:30 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-y/W+IS3pBW6YOxougP7451HTyQ/Q1y9J/D94i5Ds7u8="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.min.js.sgi57dik4g.map", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\9hizxlbrr4-sgi57dik4g.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000021685859"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "46112"}, {"Name": "ETag", "Value": "\"y/W+IS3pBW6YOxougP7451HTyQ/Q1y9J/D94i5Ds7u8=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 06:01:30 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"vMfBbEXmojM9AaHrIyKSo+20n5JM7KMyJkBCfL4pgL4=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "sgi57dik4g"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.min.js.map"}, {"Name": "integrity", "Value": "sha256-vMfBbEXmojM9AaHrIyKSo+20n5JM7KMyJkBCfL4pgL4="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.min.js.sgi57dik4g.map", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "190253"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"vMfBbEXmojM9AaHrIyKSo+20n5JM7KMyJkBCfL4pgL4=\""}, {"Name": "Last-Modified", "Value": "Wed, 14 May 2025 14:28:06 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "sgi57dik4g"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.min.js.map"}, {"Name": "integrity", "Value": "sha256-vMfBbEXmojM9AaHrIyKSo+20n5JM7KMyJkBCfL4pgL4="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.min.js.sgi57dik4g.map.gz", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\9hizxlbrr4-sgi57dik4g.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "46112"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"y/W+IS3pBW6YOxougP7451HTyQ/Q1y9J/D94i5Ds7u8=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 06:01:30 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "sgi57dik4g"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.min.js.map.gz"}, {"Name": "integrity", "Value": "sha256-y/W+IS3pBW6YOxougP7451HTyQ/Q1y9J/D94i5Ds7u8="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.min.zu238p5lxg.js", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\448jultfmw-zu238p5lxg.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000064758451"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "15441"}, {"Name": "ETag", "Value": "\"/zGCumvrl91atAvEbBmyY+KKn65ToLyg+icn9FQP8xU=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 06:01:30 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"O82ALp93hJ58HpPIcnn7uwTUWUnSvnmwNWbOrN4psVg=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "zu238p5lxg"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.min.js"}, {"Name": "integrity", "Value": "sha256-O82ALp93hJ58HpPIcnn7uwTUWUnSvnmwNWbOrN4psVg="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.min.zu238p5lxg.js", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "58078"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"O82ALp93hJ58HpPIcnn7uwTUWUnSvnmwNWbOrN4psVg=\""}, {"Name": "Last-Modified", "Value": "Wed, 14 May 2025 14:28:06 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "zu238p5lxg"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.min.js"}, {"Name": "integrity", "Value": "sha256-O82ALp93hJ58HpPIcnn7uwTUWUnSvnmwNWbOrN4psVg="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.min.zu238p5lxg.js.gz", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\448jultfmw-zu238p5lxg.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "15441"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"/zGCumvrl91atAvEbBmyY+KKn65ToLyg+icn9FQP8xU=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 06:01:30 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "zu238p5lxg"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.min.js.gz"}, {"Name": "integrity", "Value": "sha256-/zGCumvrl91atAvEbBmyY+KKn65ToLyg+icn9FQP8xU="}]}, {"Route": "lib/bootstrap/LICENSE", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\bootstrap\\LICENSE", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1153"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"iMA3h2QDzhvwmVtohviPpq8VwRv3UMY/PEE+ao7bBq0=\""}, {"Name": "Last-Modified", "Value": "Wed, 14 May 2025 14:28:06 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-iMA3h2QDzhvwmVtohviPpq8VwRv3UMY/PEE+ao7bBq0="}]}, {"Route": "lib/bootstrap/LICENSE.weyt030wr8", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\bootstrap\\LICENSE", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1153"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"iMA3h2QDzhvwmVtohviPpq8VwRv3UMY/PEE+ao7bBq0=\""}, {"Name": "Last-Modified", "Value": "Wed, 14 May 2025 14:28:06 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "weyt030wr8"}, {"Name": "label", "Value": "lib/bootstrap/LICENSE"}, {"Name": "integrity", "Value": "sha256-iMA3h2QDzhvwmVtohviPpq8VwRv3UMY/PEE+ao7bBq0="}]}, {"Route": "lib/jquery/dist/jquery.a8eee2794i.js", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\k6ix4t2cun-a8eee2794i.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000012252800"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "81613"}, {"Name": "ETag", "Value": "\"xZFJpzAOm0oPUAPPaOaOm9PjEktxu2/+op37a3inY8Y=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 06:01:30 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"igUc00PXGT1YBL1/Kf7QYy9fPlLqZKcEGrCqDz3EFDI=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "a8eee2794i"}, {"Name": "label", "Value": "lib/jquery/dist/jquery.js"}, {"Name": "integrity", "Value": "sha256-igUc00PXGT1YBL1/Kf7QYy9fPlLqZKcEGrCqDz3EFDI="}]}, {"Route": "lib/jquery/dist/jquery.a8eee2794i.js", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\jquery\\dist\\jquery.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "282115"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"igUc00PXGT1YBL1/Kf7QYy9fPlLqZKcEGrCqDz3EFDI=\""}, {"Name": "Last-Modified", "Value": "Wed, 14 May 2025 14:28:06 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "a8eee2794i"}, {"Name": "label", "Value": "lib/jquery/dist/jquery.js"}, {"Name": "integrity", "Value": "sha256-igUc00PXGT1YBL1/Kf7QYy9fPlLqZKcEGrCqDz3EFDI="}]}, {"Route": "lib/jquery/dist/jquery.a8eee2794i.js.gz", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\k6ix4t2cun-a8eee2794i.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "81613"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"xZFJpzAOm0oPUAPPaOaOm9PjEktxu2/+op37a3inY8Y=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 06:01:30 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "a8eee2794i"}, {"Name": "label", "Value": "lib/jquery/dist/jquery.js.gz"}, {"Name": "integrity", "Value": "sha256-xZFJpzAOm0oPUAPPaOaOm9PjEktxu2/+op37a3inY8Y="}]}, {"Route": "lib/jquery/dist/jquery.js", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\k6ix4t2cun-a8eee2794i.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000012252800"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "81613"}, {"Name": "ETag", "Value": "\"xZFJpzAOm0oPUAPPaOaOm9PjEktxu2/+op37a3inY8Y=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 06:01:30 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"igUc00PXGT1YBL1/Kf7QYy9fPlLqZKcEGrCqDz3EFDI=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-igUc00PXGT1YBL1/Kf7QYy9fPlLqZKcEGrCqDz3EFDI="}]}, {"Route": "lib/jquery/dist/jquery.js", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\jquery\\dist\\jquery.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "282115"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"igUc00PXGT1YBL1/Kf7QYy9fPlLqZKcEGrCqDz3EFDI=\""}, {"Name": "Last-Modified", "Value": "Wed, 14 May 2025 14:28:06 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-igUc00PXGT1YBL1/Kf7QYy9fPlLqZKcEGrCqDz3EFDI="}]}, {"Route": "lib/jquery/dist/jquery.js.gz", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\k6ix4t2cun-a8eee2794i.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "81613"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"xZFJpzAOm0oPUAPPaOaOm9PjEktxu2/+op37a3inY8Y=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 06:01:30 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-xZFJpzAOm0oPUAPPaOaOm9PjEktxu2/+op37a3inY8Y="}]}, {"Route": "lib/jquery/dist/jquery.min.b804xvqo1w.map", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\7iaxxtbiqn-b804xvqo1w.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000018862586"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "53014"}, {"Name": "ETag", "Value": "\"4BWp/edO0rIyA+BxiKPHBnzmhoRLav1dEQiSlY7ixcc=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 06:01:30 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"1eiGMA0y1kD5P/bXnTaiTKk8vtvQ8IC2nXAoI1HJXyE=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "b804xvqo1w"}, {"Name": "label", "Value": "lib/jquery/dist/jquery.min.map"}, {"Name": "integrity", "Value": "sha256-1eiGMA0y1kD5P/bXnTaiTKk8vtvQ8IC2nXAoI1HJXyE="}]}, {"Route": "lib/jquery/dist/jquery.min.b804xvqo1w.map", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\jquery\\dist\\jquery.min.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "132370"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"1eiGMA0y1kD5P/bXnTaiTKk8vtvQ8IC2nXAoI1HJXyE=\""}, {"Name": "Last-Modified", "Value": "Wed, 14 May 2025 14:28:06 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "b804xvqo1w"}, {"Name": "label", "Value": "lib/jquery/dist/jquery.min.map"}, {"Name": "integrity", "Value": "sha256-1eiGMA0y1kD5P/bXnTaiTKk8vtvQ8IC2nXAoI1HJXyE="}]}, {"Route": "lib/jquery/dist/jquery.min.b804xvqo1w.map.gz", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\7iaxxtbiqn-b804xvqo1w.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "53014"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"4BWp/edO0rIyA+BxiKPHBnzmhoRLav1dEQiSlY7ixcc=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 06:01:30 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "b804xvqo1w"}, {"Name": "label", "Value": "lib/jquery/dist/jquery.min.map.gz"}, {"Name": "integrity", "Value": "sha256-4BWp/edO0rIyA+BxiKPHBnzmhoRLav1dEQiSlY7ixcc="}]}, {"Route": "lib/jquery/dist/jquery.min.fc9074g7ds.js", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\9vs9glwrv0-fc9074g7ds.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000032939161"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "30358"}, {"Name": "ETag", "Value": "\"T6QtgY4DAKUwGXkADndGXihd1P1yhbRrSJEJmozFCKQ=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 06:01:30 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"T+aPohYXbm0fRYDpJLr+zJ9RmYTswGsahAoIsNiMld4=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "fc9074g7ds"}, {"Name": "label", "Value": "lib/jquery/dist/jquery.min.js"}, {"Name": "integrity", "Value": "sha256-T+aPohYXbm0fRYDpJLr+zJ9RmYTswGsahAoIsNiMld4="}]}, {"Route": "lib/jquery/dist/jquery.min.fc9074g7ds.js", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\jquery\\dist\\jquery.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "86929"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"T+aPohYXbm0fRYDpJLr+zJ9RmYTswGsahAoIsNiMld4=\""}, {"Name": "Last-Modified", "Value": "Wed, 14 May 2025 14:28:06 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "fc9074g7ds"}, {"Name": "label", "Value": "lib/jquery/dist/jquery.min.js"}, {"Name": "integrity", "Value": "sha256-T+aPohYXbm0fRYDpJLr+zJ9RmYTswGsahAoIsNiMld4="}]}, {"Route": "lib/jquery/dist/jquery.min.fc9074g7ds.js.gz", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\9vs9glwrv0-fc9074g7ds.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "30358"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"T6QtgY4DAKUwGXkADndGXihd1P1yhbRrSJEJmozFCKQ=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 06:01:30 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "fc9074g7ds"}, {"Name": "label", "Value": "lib/jquery/dist/jquery.min.js.gz"}, {"Name": "integrity", "Value": "sha256-T6QtgY4DAKUwGXkADndGXihd1P1yhbRrSJEJmozFCKQ="}]}, {"Route": "lib/jquery/dist/jquery.min.js", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\9vs9glwrv0-fc9074g7ds.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000032939161"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "30358"}, {"Name": "ETag", "Value": "\"T6QtgY4DAKUwGXkADndGXihd1P1yhbRrSJEJmozFCKQ=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 06:01:30 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"T+aPohYXbm0fRYDpJLr+zJ9RmYTswGsahAoIsNiMld4=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-T+aPohYXbm0fRYDpJLr+zJ9RmYTswGsahAoIsNiMld4="}]}, {"Route": "lib/jquery/dist/jquery.min.js", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\jquery\\dist\\jquery.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "86929"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"T+aPohYXbm0fRYDpJLr+zJ9RmYTswGsahAoIsNiMld4=\""}, {"Name": "Last-Modified", "Value": "Wed, 14 May 2025 14:28:06 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-T+aPohYXbm0fRYDpJLr+zJ9RmYTswGsahAoIsNiMld4="}]}, {"Route": "lib/jquery/dist/jquery.min.js.gz", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\9vs9glwrv0-fc9074g7ds.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "30358"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"T6QtgY4DAKUwGXkADndGXihd1P1yhbRrSJEJmozFCKQ=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 06:01:30 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-T6QtgY4DAKUwGXkADndGXihd1P1yhbRrSJEJmozFCKQ="}]}, {"Route": "lib/jquery/dist/jquery.min.map", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\7iaxxtbiqn-b804xvqo1w.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000018862586"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "53014"}, {"Name": "ETag", "Value": "\"4BWp/edO0rIyA+BxiKPHBnzmhoRLav1dEQiSlY7ixcc=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 06:01:30 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"1eiGMA0y1kD5P/bXnTaiTKk8vtvQ8IC2nXAoI1HJXyE=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-1eiGMA0y1kD5P/bXnTaiTKk8vtvQ8IC2nXAoI1HJXyE="}]}, {"Route": "lib/jquery/dist/jquery.min.map", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\jquery\\dist\\jquery.min.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "132370"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"1eiGMA0y1kD5P/bXnTaiTKk8vtvQ8IC2nXAoI1HJXyE=\""}, {"Name": "Last-Modified", "Value": "Wed, 14 May 2025 14:28:06 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-1eiGMA0y1kD5P/bXnTaiTKk8vtvQ8IC2nXAoI1HJXyE="}]}, {"Route": "lib/jquery/dist/jquery.min.map.gz", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\7iaxxtbiqn-b804xvqo1w.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "53014"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"4BWp/edO0rIyA+BxiKPHBnzmhoRLav1dEQiSlY7ixcc=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 06:01:30 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-4BWp/edO0rIyA+BxiKPHBnzmhoRLav1dEQiSlY7ixcc="}]}, {"Route": "lib/jquery/LICENSE.afgyafcsqt.txt", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\ey517g5lte-afgyafcsqt.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.001070663812"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "933"}, {"Name": "ETag", "Value": "\"2tL6ssgUvPLUDr8vBWmWHtk+7IqutyuHFEGAG7jexBw=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 06:01:30 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"WtKlDye/qV68IA2W1tHFTyXyc/HjQvQKBpIK1CEulFM=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "afgyafcsqt"}, {"Name": "label", "Value": "lib/jquery/LICENSE.txt"}, {"Name": "integrity", "Value": "sha256-WtKlDye/qV68IA2W1tHFTyXyc/HjQvQKBpIK1CEulFM="}]}, {"Route": "lib/jquery/LICENSE.afgyafcsqt.txt", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\jquery\\LICENSE.txt", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1641"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"WtKlDye/qV68IA2W1tHFTyXyc/HjQvQKBpIK1CEulFM=\""}, {"Name": "Last-Modified", "Value": "Wed, 14 May 2025 14:28:06 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "afgyafcsqt"}, {"Name": "label", "Value": "lib/jquery/LICENSE.txt"}, {"Name": "integrity", "Value": "sha256-WtKlDye/qV68IA2W1tHFTyXyc/HjQvQKBpIK1CEulFM="}]}, {"Route": "lib/jquery/LICENSE.afgyafcsqt.txt.gz", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\ey517g5lte-afgyafcsqt.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "933"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"2tL6ssgUvPLUDr8vBWmWHtk+7IqutyuHFEGAG7jexBw=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 06:01:30 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "afgyafcsqt"}, {"Name": "label", "Value": "lib/jquery/LICENSE.txt.gz"}, {"Name": "integrity", "Value": "sha256-2tL6ssgUvPLUDr8vBWmWHtk+7IqutyuHFEGAG7jexBw="}]}, {"Route": "lib/jquery/LICENSE.txt", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\ey517g5lte-afgyafcsqt.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.001070663812"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "933"}, {"Name": "ETag", "Value": "\"2tL6ssgUvPLUDr8vBWmWHtk+7IqutyuHFEGAG7jexBw=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 06:01:30 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"WtKlDye/qV68IA2W1tHFTyXyc/HjQvQKBpIK1CEulFM=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-WtKlDye/qV68IA2W1tHFTyXyc/HjQvQKBpIK1CEulFM="}]}, {"Route": "lib/jquery/LICENSE.txt", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\jquery\\LICENSE.txt", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1641"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"WtKlDye/qV68IA2W1tHFTyXyc/HjQvQKBpIK1CEulFM=\""}, {"Name": "Last-Modified", "Value": "Wed, 14 May 2025 14:28:06 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-WtKlDye/qV68IA2W1tHFTyXyc/HjQvQKBpIK1CEulFM="}]}, {"Route": "lib/jquery/LICENSE.txt.gz", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\ey517g5lte-afgyafcsqt.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "933"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"2tL6ssgUvPLUDr8vBWmWHtk+7IqutyuHFEGAG7jexBw=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 06:01:30 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-2tL6ssgUvPLUDr8vBWmWHtk+7IqutyuHFEGAG7jexBw="}]}, {"Route": "lib/jquery-validation/dist/additional-methods.js", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\5ikut97hnh-wus95c49fh.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000085477391"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "11698"}, {"Name": "ETag", "Value": "\"Pk7e9oX4/+O7Ztv+d0Yk/GdqClCzkF9l6oJgJLD6RgU=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 06:01:30 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"qLDpf9Urms7R6rnASrjHz38WdQfOvSOmTgLDfzQSzIQ=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-qLDpf9Urms7R6rnASrjHz38WdQfOvSOmTgLDfzQSzIQ="}]}, {"Route": "lib/jquery-validation/dist/additional-methods.js", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "43184"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"qLDpf9Urms7R6rnASrjHz38WdQfOvSOmTgLDfzQSzIQ=\""}, {"Name": "Last-Modified", "Value": "Wed, 14 May 2025 14:28:06 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-qLDpf9Urms7R6rnASrjHz38WdQfOvSOmTgLDfzQSzIQ="}]}, {"Route": "lib/jquery-validation/dist/additional-methods.js.gz", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\5ikut97hnh-wus95c49fh.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "11698"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"Pk7e9oX4/+O7Ztv+d0Yk/GdqClCzkF9l6oJgJLD6RgU=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 06:01:30 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Pk7e9oX4/+O7Ztv+d0Yk/GdqClCzkF9l6oJgJLD6RgU="}]}, {"Route": "lib/jquery-validation/dist/additional-methods.min.js", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\dvdvw0odmf-sjab29p8z5.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000183116645"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5460"}, {"Name": "ETag", "Value": "\"OLeOSYXppYtjegTqvAF1VW6jF7Rk/TBkhxwj44Fhlbc=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 06:01:30 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"2F/T6dcoSumcuA/fcU4W36VpSKPtq4nQf/0/vNFsC+w=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-2F/T6dcoSumcuA/fcU4W36VpSKPtq4nQf/0/vNFsC+w="}]}, {"Route": "lib/jquery-validation/dist/additional-methods.min.js", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "18467"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"2F/T6dcoSumcuA/fcU4W36VpSKPtq4nQf/0/vNFsC+w=\""}, {"Name": "Last-Modified", "Value": "Wed, 14 May 2025 14:28:06 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-2F/T6dcoSumcuA/fcU4W36VpSKPtq4nQf/0/vNFsC+w="}]}, {"Route": "lib/jquery-validation/dist/additional-methods.min.js.gz", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\dvdvw0odmf-sjab29p8z5.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5460"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"OLeOSYXppYtjegTqvAF1VW6jF7Rk/TBkhxwj44Fhlbc=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 06:01:30 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-OLeOSYXppYtjegTqvAF1VW6jF7Rk/TBkhxwj44Fhlbc="}]}, {"Route": "lib/jquery-validation/dist/additional-methods.min.sjab29p8z5.js", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\dvdvw0odmf-sjab29p8z5.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000183116645"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5460"}, {"Name": "ETag", "Value": "\"OLeOSYXppYtjegTqvAF1VW6jF7Rk/TBkhxwj44Fhlbc=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 06:01:30 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"2F/T6dcoSumcuA/fcU4W36VpSKPtq4nQf/0/vNFsC+w=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "sjab29p8z5"}, {"Name": "label", "Value": "lib/jquery-validation/dist/additional-methods.min.js"}, {"Name": "integrity", "Value": "sha256-2F/T6dcoSumcuA/fcU4W36VpSKPtq4nQf/0/vNFsC+w="}]}, {"Route": "lib/jquery-validation/dist/additional-methods.min.sjab29p8z5.js", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "18467"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"2F/T6dcoSumcuA/fcU4W36VpSKPtq4nQf/0/vNFsC+w=\""}, {"Name": "Last-Modified", "Value": "Wed, 14 May 2025 14:28:06 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "sjab29p8z5"}, {"Name": "label", "Value": "lib/jquery-validation/dist/additional-methods.min.js"}, {"Name": "integrity", "Value": "sha256-2F/T6dcoSumcuA/fcU4W36VpSKPtq4nQf/0/vNFsC+w="}]}, {"Route": "lib/jquery-validation/dist/additional-methods.min.sjab29p8z5.js.gz", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\dvdvw0odmf-sjab29p8z5.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5460"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"OLeOSYXppYtjegTqvAF1VW6jF7Rk/TBkhxwj44Fhlbc=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 06:01:30 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "sjab29p8z5"}, {"Name": "label", "Value": "lib/jquery-validation/dist/additional-methods.min.js.gz"}, {"Name": "integrity", "Value": "sha256-OLeOSYXppYtjegTqvAF1VW6jF7Rk/TBkhxwj44Fhlbc="}]}, {"Route": "lib/jquery-validation/dist/additional-methods.wus95c49fh.js", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\5ikut97hnh-wus95c49fh.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000085477391"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "11698"}, {"Name": "ETag", "Value": "\"Pk7e9oX4/+O7Ztv+d0Yk/GdqClCzkF9l6oJgJLD6RgU=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 06:01:30 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"qLDpf9Urms7R6rnASrjHz38WdQfOvSOmTgLDfzQSzIQ=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "wus95c49fh"}, {"Name": "label", "Value": "lib/jquery-validation/dist/additional-methods.js"}, {"Name": "integrity", "Value": "sha256-qLDpf9Urms7R6rnASrjHz38WdQfOvSOmTgLDfzQSzIQ="}]}, {"Route": "lib/jquery-validation/dist/additional-methods.wus95c49fh.js", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "43184"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"qLDpf9Urms7R6rnASrjHz38WdQfOvSOmTgLDfzQSzIQ=\""}, {"Name": "Last-Modified", "Value": "Wed, 14 May 2025 14:28:06 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "wus95c49fh"}, {"Name": "label", "Value": "lib/jquery-validation/dist/additional-methods.js"}, {"Name": "integrity", "Value": "sha256-qLDpf9Urms7R6rnASrjHz38WdQfOvSOmTgLDfzQSzIQ="}]}, {"Route": "lib/jquery-validation/dist/additional-methods.wus95c49fh.js.gz", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\5ikut97hnh-wus95c49fh.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "11698"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"Pk7e9oX4/+O7Ztv+d0Yk/GdqClCzkF9l6oJgJLD6RgU=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 06:01:30 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "wus95c49fh"}, {"Name": "label", "Value": "lib/jquery-validation/dist/additional-methods.js.gz"}, {"Name": "integrity", "Value": "sha256-Pk7e9oX4/+O7Ztv+d0Yk/GdqClCzkF9l6oJgJLD6RgU="}]}, {"Route": "lib/jquery-validation/dist/jquery.validate.d2pxujwhw3.js", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\cs0x98z2f0-d2pxujwhw3.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000076324225"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "13101"}, {"Name": "ETag", "Value": "\"LJlWNqc+gAs3U5I+jF4VyB+HKQmiECp60pjSAw/gqhs=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 06:01:30 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"27gs04nyeNuL9zc/GLQLjdbZqhNGvH+xIYgnYVPIawE=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "d2pxujwhw3"}, {"Name": "label", "Value": "lib/jquery-validation/dist/jquery.validate.js"}, {"Name": "integrity", "Value": "sha256-27gs04nyeNuL9zc/GLQLjdbZqhNGvH+xIYgnYVPIawE="}]}, {"Route": "lib/jquery-validation/dist/jquery.validate.d2pxujwhw3.js", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "50276"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"27gs04nyeNuL9zc/GLQLjdbZqhNGvH+xIYgnYVPIawE=\""}, {"Name": "Last-Modified", "Value": "Wed, 14 May 2025 14:28:06 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "d2pxujwhw3"}, {"Name": "label", "Value": "lib/jquery-validation/dist/jquery.validate.js"}, {"Name": "integrity", "Value": "sha256-27gs04nyeNuL9zc/GLQLjdbZqhNGvH+xIYgnYVPIawE="}]}, {"Route": "lib/jquery-validation/dist/jquery.validate.d2pxujwhw3.js.gz", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\cs0x98z2f0-d2pxujwhw3.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "13101"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"LJlWNqc+gAs3U5I+jF4VyB+HKQmiECp60pjSAw/gqhs=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 06:01:30 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "d2pxujwhw3"}, {"Name": "label", "Value": "lib/jquery-validation/dist/jquery.validate.js.gz"}, {"Name": "integrity", "Value": "sha256-LJlWNqc+gAs3U5I+jF4VyB+HKQmiECp60pjSAw/gqhs="}]}, {"Route": "lib/jquery-validation/dist/jquery.validate.js", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\cs0x98z2f0-d2pxujwhw3.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000076324225"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "13101"}, {"Name": "ETag", "Value": "\"LJlWNqc+gAs3U5I+jF4VyB+HKQmiECp60pjSAw/gqhs=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 06:01:30 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"27gs04nyeNuL9zc/GLQLjdbZqhNGvH+xIYgnYVPIawE=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-27gs04nyeNuL9zc/GLQLjdbZqhNGvH+xIYgnYVPIawE="}]}, {"Route": "lib/jquery-validation/dist/jquery.validate.js", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "50276"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"27gs04nyeNuL9zc/GLQLjdbZqhNGvH+xIYgnYVPIawE=\""}, {"Name": "Last-Modified", "Value": "Wed, 14 May 2025 14:28:06 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-27gs04nyeNuL9zc/GLQLjdbZqhNGvH+xIYgnYVPIawE="}]}, {"Route": "lib/jquery-validation/dist/jquery.validate.js.gz", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\cs0x98z2f0-d2pxujwhw3.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "13101"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"LJlWNqc+gAs3U5I+jF4VyB+HKQmiECp60pjSAw/gqhs=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 06:01:30 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-LJlWNqc+gAs3U5I+jF4VyB+HKQmiECp60pjSAw/gqhs="}]}, {"Route": "lib/jquery-validation/dist/jquery.validate.min.4dm14o4hmc.js", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\ovqzhavyxs-4dm14o4hmc.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000133226752"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "7505"}, {"Name": "ETag", "Value": "\"WEtE86OkwYRC2CNB1ewkbZ+Kqi1+CF3HTu2hd7e2V+k=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 06:01:30 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"eItLFOyfQ4d/OGzEnGchi2ZMVF8EhGgzS0k7fSOPifQ=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "4dm14o4hmc"}, {"Name": "label", "Value": "lib/jquery-validation/dist/jquery.validate.min.js"}, {"Name": "integrity", "Value": "sha256-eItLFOyfQ4d/OGzEnGchi2ZMVF8EhGgzS0k7fSOPifQ="}]}, {"Route": "lib/jquery-validation/dist/jquery.validate.min.4dm14o4hmc.js", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "23264"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"eItLFOyfQ4d/OGzEnGchi2ZMVF8EhGgzS0k7fSOPifQ=\""}, {"Name": "Last-Modified", "Value": "Wed, 14 May 2025 14:28:06 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "4dm14o4hmc"}, {"Name": "label", "Value": "lib/jquery-validation/dist/jquery.validate.min.js"}, {"Name": "integrity", "Value": "sha256-eItLFOyfQ4d/OGzEnGchi2ZMVF8EhGgzS0k7fSOPifQ="}]}, {"Route": "lib/jquery-validation/dist/jquery.validate.min.4dm14o4hmc.js.gz", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\ovqzhavyxs-4dm14o4hmc.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "7505"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"WEtE86OkwYRC2CNB1ewkbZ+Kqi1+CF3HTu2hd7e2V+k=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 06:01:30 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "4dm14o4hmc"}, {"Name": "label", "Value": "lib/jquery-validation/dist/jquery.validate.min.js.gz"}, {"Name": "integrity", "Value": "sha256-WEtE86OkwYRC2CNB1ewkbZ+Kqi1+CF3HTu2hd7e2V+k="}]}, {"Route": "lib/jquery-validation/dist/jquery.validate.min.js", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\ovqzhavyxs-4dm14o4hmc.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000133226752"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "7505"}, {"Name": "ETag", "Value": "\"WEtE86OkwYRC2CNB1ewkbZ+Kqi1+CF3HTu2hd7e2V+k=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 06:01:30 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"eItLFOyfQ4d/OGzEnGchi2ZMVF8EhGgzS0k7fSOPifQ=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-eItLFOyfQ4d/OGzEnGchi2ZMVF8EhGgzS0k7fSOPifQ="}]}, {"Route": "lib/jquery-validation/dist/jquery.validate.min.js", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "23264"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"eItLFOyfQ4d/OGzEnGchi2ZMVF8EhGgzS0k7fSOPifQ=\""}, {"Name": "Last-Modified", "Value": "Wed, 14 May 2025 14:28:06 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-eItLFOyfQ4d/OGzEnGchi2ZMVF8EhGgzS0k7fSOPifQ="}]}, {"Route": "lib/jquery-validation/dist/jquery.validate.min.js.gz", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\ovqzhavyxs-4dm14o4hmc.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "7505"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"WEtE86OkwYRC2CNB1ewkbZ+Kqi1+CF3HTu2hd7e2V+k=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 06:01:30 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-WEtE86OkwYRC2CNB1ewkbZ+Kqi1+CF3HTu2hd7e2V+k="}]}, {"Route": "lib/jquery-validation/LICENSE.md", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\7981pj8p43-x0q3zqp4vz.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.001490312966"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "670"}, {"Name": "ETag", "Value": "\"yZU1lf/p2dEnCHUXYp8G4roAyod23x5MoR4DehwHfrA=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 06:01:30 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/markdown"}, {"Name": "ETag", "Value": "W/\"geHEkw/WGPdaHQMRq5HuNY9snliNzU/y2OW8ycnhGXw=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-geHEkw/WGPdaHQMRq5HuNY9snliNzU/y2OW8ycnhGXw="}]}, {"Route": "lib/jquery-validation/LICENSE.md", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\jquery-validation\\LICENSE.md", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1117"}, {"Name": "Content-Type", "Value": "text/markdown"}, {"Name": "ETag", "Value": "\"geHEkw/WGPdaHQMRq5HuNY9snliNzU/y2OW8ycnhGXw=\""}, {"Name": "Last-Modified", "Value": "Wed, 14 May 2025 14:28:06 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-geHEkw/WGPdaHQMRq5HuNY9snliNzU/y2OW8ycnhGXw="}]}, {"Route": "lib/jquery-validation/LICENSE.md.gz", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\7981pj8p43-x0q3zqp4vz.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "670"}, {"Name": "Content-Type", "Value": "text/markdown"}, {"Name": "ETag", "Value": "\"yZU1lf/p2dEnCHUXYp8G4roAyod23x5MoR4DehwHfrA=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 06:01:30 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-yZU1lf/p2dEnCHUXYp8G4roAyod23x5MoR4DehwHfrA="}]}, {"Route": "lib/jquery-validation/LICENSE.x0q3zqp4vz.md", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\7981pj8p43-x0q3zqp4vz.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.001490312966"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "670"}, {"Name": "ETag", "Value": "\"yZU1lf/p2dEnCHUXYp8G4roAyod23x5MoR4DehwHfrA=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 06:01:30 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/markdown"}, {"Name": "ETag", "Value": "W/\"geHEkw/WGPdaHQMRq5HuNY9snliNzU/y2OW8ycnhGXw=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "x0q3zqp4vz"}, {"Name": "label", "Value": "lib/jquery-validation/LICENSE.md"}, {"Name": "integrity", "Value": "sha256-geHEkw/WGPdaHQMRq5HuNY9snliNzU/y2OW8ycnhGXw="}]}, {"Route": "lib/jquery-validation/LICENSE.x0q3zqp4vz.md", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\jquery-validation\\LICENSE.md", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1117"}, {"Name": "Content-Type", "Value": "text/markdown"}, {"Name": "ETag", "Value": "\"geHEkw/WGPdaHQMRq5HuNY9snliNzU/y2OW8ycnhGXw=\""}, {"Name": "Last-Modified", "Value": "Wed, 14 May 2025 14:28:06 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "x0q3zqp4vz"}, {"Name": "label", "Value": "lib/jquery-validation/LICENSE.md"}, {"Name": "integrity", "Value": "sha256-geHEkw/WGPdaHQMRq5HuNY9snliNzU/y2OW8ycnhGXw="}]}, {"Route": "lib/jquery-validation/LICENSE.x0q3zqp4vz.md.gz", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\7981pj8p43-x0q3zqp4vz.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "670"}, {"Name": "Content-Type", "Value": "text/markdown"}, {"Name": "ETag", "Value": "\"yZU1lf/p2dEnCHUXYp8G4roAyod23x5MoR4DehwHfrA=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 06:01:30 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "x0q3zqp4vz"}, {"Name": "label", "Value": "lib/jquery-validation/LICENSE.md.gz"}, {"Name": "integrity", "Value": "sha256-yZU1lf/p2dEnCHUXYp8G4roAyod23x5MoR4DehwHfrA="}]}, {"Route": "lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.dvc2bfcndg.js", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\bt4acyjzvx-dvc2bfcndg.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000213401622"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "4685"}, {"Name": "ETag", "Value": "\"tra5GpUITUrALtnm990ZmXUgYSqsrYHJ2wct2dSXNwo=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 06:01:30 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"qbS02vMHZxdLNYKUtLPSYaSHXj1/ZwH1fv9f3XAY0LU=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "dvc2bfcndg"}, {"Name": "label", "Value": "lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.js"}, {"Name": "integrity", "Value": "sha256-qbS02vMHZxdLNYKUtLPSYaSHXj1/ZwH1fv9f3XAY0LU="}]}, {"Route": "lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.dvc2bfcndg.js", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\jquery-validation-unobtrusive\\jquery.validate.unobtrusive.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "19798"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"qbS02vMHZxdLNYKUtLPSYaSHXj1/ZwH1fv9f3XAY0LU=\""}, {"Name": "Last-Modified", "Value": "Wed, 14 May 2025 14:28:06 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "dvc2bfcndg"}, {"Name": "label", "Value": "lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.js"}, {"Name": "integrity", "Value": "sha256-qbS02vMHZxdLNYKUtLPSYaSHXj1/ZwH1fv9f3XAY0LU="}]}, {"Route": "lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.dvc2bfcndg.js.gz", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\bt4acyjzvx-dvc2bfcndg.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "4685"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"tra5GpUITUrALtnm990ZmXUgYSqsrYHJ2wct2dSXNwo=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 06:01:30 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "dvc2bfcndg"}, {"Name": "label", "Value": "lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.js.gz"}, {"Name": "integrity", "Value": "sha256-tra5GpUITUrALtnm990ZmXUgYSqsrYHJ2wct2dSXNwo="}]}, {"Route": "lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.js", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\bt4acyjzvx-dvc2bfcndg.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000213401622"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "4685"}, {"Name": "ETag", "Value": "\"tra5GpUITUrALtnm990ZmXUgYSqsrYHJ2wct2dSXNwo=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 06:01:30 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"qbS02vMHZxdLNYKUtLPSYaSHXj1/ZwH1fv9f3XAY0LU=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-qbS02vMHZxdLNYKUtLPSYaSHXj1/ZwH1fv9f3XAY0LU="}]}, {"Route": "lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.js", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\jquery-validation-unobtrusive\\jquery.validate.unobtrusive.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "19798"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"qbS02vMHZxdLNYKUtLPSYaSHXj1/ZwH1fv9f3XAY0LU=\""}, {"Name": "Last-Modified", "Value": "Wed, 14 May 2025 14:28:06 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-qbS02vMHZxdLNYKUtLPSYaSHXj1/ZwH1fv9f3XAY0LU="}]}, {"Route": "lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.js.gz", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\bt4acyjzvx-dvc2bfcndg.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "4685"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"tra5GpUITUrALtnm990ZmXUgYSqsrYHJ2wct2dSXNwo=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 06:01:30 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-tra5GpUITUrALtnm990ZmXUgYSqsrYHJ2wct2dSXNwo="}]}, {"Route": "lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.min.gb7ocvbhts.js", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\xd5r9ktl5s-gb7ocvbhts.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000450247636"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2220"}, {"Name": "ETag", "Value": "\"+uJmiWNDfG3y+8SUapZ3/8iFhrZEO4UoNTds6PJqncM=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 06:01:30 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"9GycpJnliUjJDVDqP0UEu/bsm9U+3dnQUH8+3W10vkY=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "gb7ocvbhts"}, {"Name": "label", "Value": "lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.min.js"}, {"Name": "integrity", "Value": "sha256-9GycpJnliUjJDVDqP0UEu/bsm9U+3dnQUH8+3W10vkY="}]}, {"Route": "lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.min.gb7ocvbhts.js", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\jquery-validation-unobtrusive\\jquery.validate.unobtrusive.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5871"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"9GycpJnliUjJDVDqP0UEu/bsm9U+3dnQUH8+3W10vkY=\""}, {"Name": "Last-Modified", "Value": "Wed, 14 May 2025 14:28:06 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "gb7ocvbhts"}, {"Name": "label", "Value": "lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.min.js"}, {"Name": "integrity", "Value": "sha256-9GycpJnliUjJDVDqP0UEu/bsm9U+3dnQUH8+3W10vkY="}]}, {"Route": "lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.min.gb7ocvbhts.js.gz", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\xd5r9ktl5s-gb7ocvbhts.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2220"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"+uJmiWNDfG3y+8SUapZ3/8iFhrZEO4UoNTds6PJqncM=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 06:01:30 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "gb7ocvbhts"}, {"Name": "label", "Value": "lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.min.js.gz"}, {"Name": "integrity", "Value": "sha256-+uJmiWNDfG3y+8SUapZ3/8iFhrZEO4UoNTds6PJqncM="}]}, {"Route": "lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.min.js", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\xd5r9ktl5s-gb7ocvbhts.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000450247636"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2220"}, {"Name": "ETag", "Value": "\"+uJmiWNDfG3y+8SUapZ3/8iFhrZEO4UoNTds6PJqncM=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 06:01:30 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"9GycpJnliUjJDVDqP0UEu/bsm9U+3dnQUH8+3W10vkY=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-9GycpJnliUjJDVDqP0UEu/bsm9U+3dnQUH8+3W10vkY="}]}, {"Route": "lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.min.js", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\jquery-validation-unobtrusive\\jquery.validate.unobtrusive.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5871"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"9GycpJnliUjJDVDqP0UEu/bsm9U+3dnQUH8+3W10vkY=\""}, {"Name": "Last-Modified", "Value": "Wed, 14 May 2025 14:28:06 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-9GycpJnliUjJDVDqP0UEu/bsm9U+3dnQUH8+3W10vkY="}]}, {"Route": "lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.min.js.gz", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\xd5r9ktl5s-gb7ocvbhts.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2220"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"+uJmiWNDfG3y+8SUapZ3/8iFhrZEO4UoNTds6PJqncM=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 06:01:30 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-+uJmiWNDfG3y+8SUapZ3/8iFhrZEO4UoNTds6PJqncM="}]}, {"Route": "lib/jquery-validation-unobtrusive/LICENSE.txt", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\92bk6dxr5j-wqejeusuyq.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.002525252525"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "395"}, {"Name": "ETag", "Value": "\"0k+GY7B37PlcFstK7JIg2ptnyI+BsfB92+jtjLIJOuo=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 06:01:30 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"aBc/n/nO9esvq0e80G57lEYLonvE5dKKUgoozaiVfLM=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-aBc/n/nO9esvq0e80G57lEYLonvE5dKKUgoozaiVfLM="}]}, {"Route": "lib/jquery-validation-unobtrusive/LICENSE.txt", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "587"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"aBc/n/nO9esvq0e80G57lEYLonvE5dKKUgoozaiVfLM=\""}, {"Name": "Last-Modified", "Value": "Wed, 14 May 2025 14:28:06 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-aBc/n/nO9esvq0e80G57lEYLonvE5dKKUgoozaiVfLM="}]}, {"Route": "lib/jquery-validation-unobtrusive/LICENSE.txt.gz", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\92bk6dxr5j-wqejeusuyq.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "395"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"0k+GY7B37PlcFstK7JIg2ptnyI+BsfB92+jtjLIJOuo=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 06:01:30 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-0k+GY7B37PlcFstK7JIg2ptnyI+BsfB92+jtjLIJOuo="}]}, {"Route": "lib/jquery-validation-unobtrusive/LICENSE.wqejeusuyq.txt", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\92bk6dxr5j-wqejeusuyq.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.002525252525"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "395"}, {"Name": "ETag", "Value": "\"0k+GY7B37PlcFstK7JIg2ptnyI+BsfB92+jtjLIJOuo=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 06:01:30 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"aBc/n/nO9esvq0e80G57lEYLonvE5dKKUgoozaiVfLM=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "w<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"Name": "label", "Value": "lib/jquery-validation-unobtrusive/LICENSE.txt"}, {"Name": "integrity", "Value": "sha256-aBc/n/nO9esvq0e80G57lEYLonvE5dKKUgoozaiVfLM="}]}, {"Route": "lib/jquery-validation-unobtrusive/LICENSE.wqejeusuyq.txt", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "587"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"aBc/n/nO9esvq0e80G57lEYLonvE5dKKUgoozaiVfLM=\""}, {"Name": "Last-Modified", "Value": "Wed, 14 May 2025 14:28:06 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "w<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"Name": "label", "Value": "lib/jquery-validation-unobtrusive/LICENSE.txt"}, {"Name": "integrity", "Value": "sha256-aBc/n/nO9esvq0e80G57lEYLonvE5dKKUgoozaiVfLM="}]}, {"Route": "lib/jquery-validation-unobtrusive/LICENSE.wqejeusuyq.txt.gz", "AssetFile": "H:\\SAML\\AuthDemo\\Saml2AuthDemo\\obj\\Debug\\net9.0\\compressed\\92bk6dxr5j-wqejeusuyq.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "395"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"0k+GY7B37PlcFstK7JIg2ptnyI+BsfB92+jtjLIJOuo=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 06:01:30 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "w<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"Name": "label", "Value": "lib/jquery-validation-unobtrusive/LICENSE.txt.gz"}, {"Name": "integrity", "Value": "sha256-0k+GY7B37PlcFstK7JIg2ptnyI+BsfB92+jtjLIJOuo="}]}]}