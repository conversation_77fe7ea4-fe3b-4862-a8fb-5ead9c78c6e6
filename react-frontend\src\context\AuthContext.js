import React, { createContext, useContext, useState, useEffect } from 'react';
import { authService } from '../services/authService';

const AuthContext = createContext();

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [claims, setClaims] = useState([]);

  useEffect(() => {
    checkAuthStatus();
  }, []);

  const checkAuthStatus = async () => {
    try {
      setIsLoading(true);
      const authStatus = await authService.checkAuthStatus();
      if (authStatus.isAuthenticated) {
        setUser(authStatus.user);
        setIsAuthenticated(true);
        setClaims(authStatus.claims || []);
      } else {
        // For demo purposes, you can uncomment the next line to simulate authentication
        // simulateAuthentication();
      }
    } catch (error) {
      console.error('Error checking auth status:', error);
      // For demo purposes, fall back to mock data if backend is not available
      simulateAuthentication();
    } finally {
      setIsLoading(false);
    }
  };

  const simulateAuthentication = () => {
    const mockData = authService.getMockAuthData();
    setUser(mockData.user);
    setIsAuthenticated(mockData.isAuthenticated);
    setClaims(mockData.claims);
  };

  const login = async () => {
    try {
      // Redirect to SAML login endpoint
      const currentUrl = window.location.href;
      const loginUrl = authService.getLoginUrl(currentUrl);
      window.location.href = loginUrl;
    } catch (error) {
      console.error('Login error:', error);
    }
  };

  const logout = async () => {
    try {
      await authService.logout();
      setUser(null);
      setIsAuthenticated(false);
      setClaims([]);
      // Redirect to home page
      window.location.href = '/';
    } catch (error) {
      console.error('Logout error:', error);
    }
  };

  const value = {
    user,
    isAuthenticated,
    isLoading,
    claims,
    login,
    logout,
    checkAuthStatus,
    simulateAuthentication
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};
