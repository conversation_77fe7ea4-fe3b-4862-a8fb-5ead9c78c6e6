# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.js

# testing
/coverage

# production
/build

# misc
.DS_Store
.env.local
.env.development.local
.env.test.local
.env.production.local

# Environment files (keep .env and .env.development for development)
# .env
# .env.development

npm-debug.log*
yarn-debug.log*
yarn-error.log*
