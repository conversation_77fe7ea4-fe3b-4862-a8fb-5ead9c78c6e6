{"runtimeTarget": {"name": ".NETCoreApp,Version=v9.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v9.0": {"Saml2AuthDemo/1.0.0": {"dependencies": {"ITfoxtec.Identity.Saml2": "4.15.3", "ITfoxtec.Identity.Saml2.MvcCore": "4.15.3"}, "runtime": {"Saml2AuthDemo.dll": {}}}, "ITfoxtec.Identity.Saml2/4.15.3": {"dependencies": {"Microsoft.Extensions.Http": "9.0.0", "Microsoft.IdentityModel.Tokens.Saml": "8.2.1", "System.Security.Cryptography.Xml": "9.0.0", "System.ServiceModel.Security": "6.0.0"}, "runtime": {"lib/net9.0/ITfoxtec.Identity.Saml2.dll": {"assemblyVersion": "4.15.3.0", "fileVersion": "4.15.3.0"}}}, "ITfoxtec.Identity.Saml2.MvcCore/4.15.3": {"dependencies": {"ITfoxtec.Identity.Saml2": "4.15.3"}, "runtime": {"lib/net9.0/ITfoxtec.Identity.Saml2.MvcCore.dll": {"assemblyVersion": "4.15.3.0", "fileVersion": "4.15.3.0"}}}, "Microsoft.Extensions.Configuration/9.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.0", "Microsoft.Extensions.Primitives": "9.0.0"}}, "Microsoft.Extensions.Configuration.Abstractions/9.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.0"}}, "Microsoft.Extensions.Configuration.Binder/9.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.0"}}, "Microsoft.Extensions.DependencyInjection/9.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.0"}}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.0": {}, "Microsoft.Extensions.Diagnostics/9.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.0", "Microsoft.Extensions.Diagnostics.Abstractions": "9.0.0", "Microsoft.Extensions.Options.ConfigurationExtensions": "9.0.0"}}, "Microsoft.Extensions.Diagnostics.Abstractions/9.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.0", "Microsoft.Extensions.Options": "9.0.0"}}, "Microsoft.Extensions.Http/9.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.0", "Microsoft.Extensions.Diagnostics": "9.0.0", "Microsoft.Extensions.Logging": "9.0.0", "Microsoft.Extensions.Logging.Abstractions": "9.0.0", "Microsoft.Extensions.Options": "9.0.0"}}, "Microsoft.Extensions.Logging/9.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection": "9.0.0", "Microsoft.Extensions.Logging.Abstractions": "9.0.0", "Microsoft.Extensions.Options": "9.0.0"}}, "Microsoft.Extensions.Logging.Abstractions/9.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.0"}}, "Microsoft.Extensions.ObjectPool/6.0.16": {}, "Microsoft.Extensions.Options/9.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.0", "Microsoft.Extensions.Primitives": "9.0.0"}}, "Microsoft.Extensions.Options.ConfigurationExtensions/9.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.0", "Microsoft.Extensions.Configuration.Binder": "9.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.0", "Microsoft.Extensions.Options": "9.0.0", "Microsoft.Extensions.Primitives": "9.0.0"}}, "Microsoft.Extensions.Primitives/9.0.0": {}, "Microsoft.IdentityModel.Abstractions/8.2.1": {"runtime": {"lib/net9.0/Microsoft.IdentityModel.Abstractions.dll": {"assemblyVersion": "8.2.1.0", "fileVersion": "8.2.1.51115"}}}, "Microsoft.IdentityModel.Logging/8.2.1": {"dependencies": {"Microsoft.IdentityModel.Abstractions": "8.2.1"}, "runtime": {"lib/net9.0/Microsoft.IdentityModel.Logging.dll": {"assemblyVersion": "8.2.1.0", "fileVersion": "8.2.1.51115"}}}, "Microsoft.IdentityModel.Tokens/8.2.1": {"dependencies": {"Microsoft.IdentityModel.Logging": "8.2.1"}, "runtime": {"lib/net9.0/Microsoft.IdentityModel.Tokens.dll": {"assemblyVersion": "8.2.1.0", "fileVersion": "8.2.1.51115"}}}, "Microsoft.IdentityModel.Tokens.Saml/8.2.1": {"dependencies": {"Microsoft.IdentityModel.Tokens": "8.2.1", "Microsoft.IdentityModel.Xml": "8.2.1"}, "runtime": {"lib/net9.0/Microsoft.IdentityModel.Tokens.Saml.dll": {"assemblyVersion": "8.2.1.0", "fileVersion": "8.2.1.51115"}}}, "Microsoft.IdentityModel.Xml/8.2.1": {"dependencies": {"Microsoft.IdentityModel.Tokens": "8.2.1"}, "runtime": {"lib/net9.0/Microsoft.IdentityModel.Xml.dll": {"assemblyVersion": "8.2.1.0", "fileVersion": "8.2.1.51115"}}}, "System.Reflection.DispatchProxy/4.7.1": {}, "System.Security.Cryptography.Pkcs/9.0.0": {}, "System.Security.Cryptography.Xml/9.0.0": {"dependencies": {"System.Security.Cryptography.Pkcs": "9.0.0"}}, "System.ServiceModel.Primitives/6.0.0": {"dependencies": {"Microsoft.Extensions.ObjectPool": "6.0.16", "System.Reflection.DispatchProxy": "4.7.1", "System.Security.Cryptography.Xml": "9.0.0"}, "runtime": {"lib/net6.0/System.ServiceModel.Primitives.dll": {"assemblyVersion": "6.0.0.0", "fileVersion": "6.0.23.25304"}}, "resources": {"lib/net6.0/cs/System.ServiceModel.Primitives.resources.dll": {"locale": "cs"}, "lib/net6.0/de/System.ServiceModel.Primitives.resources.dll": {"locale": "de"}, "lib/net6.0/es/System.ServiceModel.Primitives.resources.dll": {"locale": "es"}, "lib/net6.0/fr/System.ServiceModel.Primitives.resources.dll": {"locale": "fr"}, "lib/net6.0/it/System.ServiceModel.Primitives.resources.dll": {"locale": "it"}, "lib/net6.0/ja/System.ServiceModel.Primitives.resources.dll": {"locale": "ja"}, "lib/net6.0/ko/System.ServiceModel.Primitives.resources.dll": {"locale": "ko"}, "lib/net6.0/pl/System.ServiceModel.Primitives.resources.dll": {"locale": "pl"}, "lib/net6.0/pt-BR/System.ServiceModel.Primitives.resources.dll": {"locale": "pt-BR"}, "lib/net6.0/ru/System.ServiceModel.Primitives.resources.dll": {"locale": "ru"}, "lib/net6.0/tr/System.ServiceModel.Primitives.resources.dll": {"locale": "tr"}, "lib/net6.0/zh-Hans/System.ServiceModel.Primitives.resources.dll": {"locale": "zh-Hans"}, "lib/net6.0/zh-Hant/System.ServiceModel.Primitives.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "System.ServiceModel.Security/6.0.0": {"dependencies": {"System.ServiceModel.Primitives": "6.0.0"}, "runtime": {"lib/net6.0/System.ServiceModel.Security.dll": {"assemblyVersion": "6.0.0.0", "fileVersion": "6.0.23.25304"}}}}}, "libraries": {"Saml2AuthDemo/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "ITfoxtec.Identity.Saml2/4.15.3": {"type": "package", "serviceable": true, "sha512": "sha512-ML5YSN0gqccHQwWlkp79cPLLyghXI1r2jDQpsXYTBz1ML+Mnz/NU4RD7xjkvt33fjaEJqEy7fdzLas47RauBXg==", "path": "itfoxtec.identity.saml2/4.15.3", "hashPath": "itfoxtec.identity.saml2.4.15.3.nupkg.sha512"}, "ITfoxtec.Identity.Saml2.MvcCore/4.15.3": {"type": "package", "serviceable": true, "sha512": "sha512-0Snd2/mJuiSRdMFxTRhfGwOwBv33AIZj2/RUJYIOInmQNC5nIp761tTApCadIESxK0MulIOIjQr/MZTFTp4hSA==", "path": "itfoxtec.identity.saml2.mvccore/4.15.3", "hashPath": "itfoxtec.identity.saml2.mvccore.4.15.3.nupkg.sha512"}, "Microsoft.Extensions.Configuration/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-YIMO9T3JL8MeEXgVozKt2v79hquo/EFtnY0vgxmLnUvk1Rei/halI7kOWZL2RBeV9FMGzgM9LZA8CVaNwFMaNA==", "path": "microsoft.extensions.configuration/9.0.0", "hashPath": "microsoft.extensions.configuration.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Abstractions/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-lqvd7W3FGKUO1+ZoUEMaZ5XDJeWvjpy2/M/ptCGz3tXLD4HWVaSzjufsAsjemasBEg+2SxXVtYVvGt5r2nKDlg==", "path": "microsoft.extensions.configuration.abstractions/9.0.0", "hashPath": "microsoft.extensions.configuration.abstractions.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Binder/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-RiScL99DcyngY9zJA2ROrri7Br8tn5N4hP4YNvGdTN/bvg1A3dwvDOxHnNZ3Im7x2SJ5i4LkX1uPiR/MfSFBLQ==", "path": "microsoft.extensions.configuration.binder/9.0.0", "hashPath": "microsoft.extensions.configuration.binder.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-MCPrg7v3QgNMr0vX4vzRXvkNGgLg8vKWX0nKCWUxu2uPyMsaRgiRc1tHBnbTcfJMhMKj2slE/j2M9oGkd25DNw==", "path": "microsoft.extensions.dependencyinjection/9.0.0", "hashPath": "microsoft.extensions.dependencyinjection.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-+6f2qv2a3dLwd5w6JanPIPs47CxRbnk+ZocMJUhv9NxP88VlOcJYZs9jY+MYSjxvady08bUZn6qgiNh7DadGgg==", "path": "microsoft.extensions.dependencyinjection.abstractions/9.0.0", "hashPath": "microsoft.extensions.dependencyinjection.abstractions.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.Diagnostics/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-0CF9ZrNw5RAlRfbZuVIvzzhP8QeWqHiUmMBU/2H7Nmit8/vwP3/SbHeEctth7D4Gz2fBnEbokPc1NU8/j/1ZLw==", "path": "microsoft.extensions.diagnostics/9.0.0", "hashPath": "microsoft.extensions.diagnostics.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.Diagnostics.Abstractions/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-************************************cpgiQNY/HlDAlnrhR9dvlURfFz428A+RTCJpUyB+aKTA6AgVcQ==", "path": "microsoft.extensions.diagnostics.abstractions/9.0.0", "hashPath": "microsoft.extensions.diagnostics.abstractions.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.Http/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-DqI4q54U4hH7bIAq9M5a/hl5Odr/KBAoaZ0dcT4OgutD8dook34CbkvAfAIzkMVjYXiL+E5ul9etwwqiX4PHGw==", "path": "microsoft.extensions.http/9.0.0", "hashPath": "microsoft.extensions.http.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-crj<PERSON>yORoug0kK7RSNJBTeSE6VX8IQgLf3nUpTB9m62bPXp/tzbnOsnbe8TXEG0AASNaKZddnpHKw7fET8E++Pg==", "path": "microsoft.extensions.logging/9.0.0", "hashPath": "microsoft.extensions.logging.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.Abstractions/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-g0UfujELzlLbHoVG8kPKVBaW470Ewi+jnptGS9KUi6jcb+k2StujtK3m26DFSGGwQ/+bVgZfsWqNzlP6YOejvw==", "path": "microsoft.extensions.logging.abstractions/9.0.0", "hashPath": "microsoft.extensions.logging.abstractions.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.ObjectPool/6.0.16": {"type": "package", "serviceable": true, "sha512": "sha512-OVX5tlKg6LY+XKqlUn7i9KY+6Liut0iewWff2DNr7129i/NJ8rpUzbmxavPydZgcLREEWHklXZiPKCS895tNIQ==", "path": "microsoft.extensions.objectpool/6.0.16", "hashPath": "microsoft.extensions.objectpool.6.0.16.nupkg.sha512"}, "Microsoft.Extensions.Options/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-y2146b3jrPI3Q0lokKXdKLpmXqakYbDIPDV6r3M8SqvSf45WwOTzkyfDpxnZXJsJQEpAsAqjUq5Pu8RCJMjubg==", "path": "microsoft.extensions.options/9.0.0", "hashPath": "microsoft.extensions.options.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.Options.ConfigurationExtensions/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-Ob3FXsXkcSMQmGZi7qP07EQ39kZpSBlTcAZLbJLdI4FIf0Jug8biv2HTavWmnTirchctPlq9bl/26CXtQRguzA==", "path": "microsoft.extensions.options.configurationextensions/9.0.0", "hashPath": "microsoft.extensions.options.configurationextensions.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.Primitives/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-N3qEBzmLMYiASUlKxxFIISP4AiwuPTHF5uCh+2CWSwwzAJiIYx0kBJsS30cp1nvhSySFAVi30jecD307jV+8Kg==", "path": "microsoft.extensions.primitives/9.0.0", "hashPath": "microsoft.extensions.primitives.9.0.0.nupkg.sha512"}, "Microsoft.IdentityModel.Abstractions/8.2.1": {"type": "package", "serviceable": true, "sha512": "sha512-8sMlmHhh5HdP3+yCSCUpJpN1yYrJ6J/V39df9siY8PeMckRMrSBRL/TMs/Jex6P1ly/Ie2mFqvhcPHHrNmCd/w==", "path": "microsoft.identitymodel.abstractions/8.2.1", "hashPath": "microsoft.identitymodel.abstractions.8.2.1.nupkg.sha512"}, "Microsoft.IdentityModel.Logging/8.2.1": {"type": "package", "serviceable": true, "sha512": "sha512-EgSEAtBoWBynACdhKnMlVAFGGWqOIdmbpW7Vvx2SQ7u7ogZ50NcEGSoGljEsQoGIRYpo0UxXYktKcYMp+G/Bcg==", "path": "microsoft.identitymodel.logging/8.2.1", "hashPath": "microsoft.identitymodel.logging.8.2.1.nupkg.sha512"}, "Microsoft.IdentityModel.Tokens/8.2.1": {"type": "package", "serviceable": true, "sha512": "sha512-oQeLWCATuVXOCdIvouM4GG2xl1YNng+uAxYwu7CG6RuW+y+1+slXrOBq5csTU2pnV2SH3B1GmugDf6Jv/lexjw==", "path": "microsoft.identitymodel.tokens/8.2.1", "hashPath": "microsoft.identitymodel.tokens.8.2.1.nupkg.sha512"}, "Microsoft.IdentityModel.Tokens.Saml/8.2.1": {"type": "package", "serviceable": true, "sha512": "sha512-KBMpJoG8kX3DgBeH7MZqXQjj0IvlwOT2ZE35Y1ITwc6t5ddf0Qs5wIjTh9YM/audERUmOAXvbsSack4S8zs7hw==", "path": "microsoft.identitymodel.tokens.saml/8.2.1", "hashPath": "microsoft.identitymodel.tokens.saml.8.2.1.nupkg.sha512"}, "Microsoft.IdentityModel.Xml/8.2.1": {"type": "package", "serviceable": true, "sha512": "sha512-azmFjZpx6yAEv/DNKB+Zw8D6085t6uKiyTZiAr+ao+II3Uz1vG1bMIomhUKDhROfa8yButcAgkJWpUaygCP2MA==", "path": "microsoft.identitymodel.xml/8.2.1", "hashPath": "microsoft.identitymodel.xml.8.2.1.nupkg.sha512"}, "System.Reflection.DispatchProxy/4.7.1": {"type": "package", "serviceable": true, "sha512": "sha512-C1sMLwIG6ILQ2bmOT4gh62V6oJlyF4BlHcVMrOoor49p0Ji2tA8QAoqyMcIhAdH6OHKJ8m7BU+r4LK2CUEOKqw==", "path": "system.reflection.dispatchproxy/4.7.1", "hashPath": "system.reflection.dispatchproxy.4.7.1.nupkg.sha512"}, "System.Security.Cryptography.Pkcs/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-8tluJF8w9si+2yoHeL8rgVJS6lKvWomTDC8px65Z8MCzzdME5eaPtEQf4OfVGrAxB5fW93ncucy1+221O9EQaw==", "path": "system.security.cryptography.pkcs/9.0.0", "hashPath": "system.security.cryptography.pkcs.9.0.0.nupkg.sha512"}, "System.Security.Cryptography.Xml/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-GQZn5wFd+pyOfwWaCbqxG7trQ5ox01oR8kYgWflgtux4HiUNihGEgG2TktRWyH+9bw7NoEju1D41H/upwQeFQw==", "path": "system.security.cryptography.xml/9.0.0", "hashPath": "system.security.cryptography.xml.9.0.0.nupkg.sha512"}, "System.ServiceModel.Primitives/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-uGyB5FMdwiBmSgJtB0x9mWUuGYj/RbXjh8I6FtsZmUAVcFc6qjOasHPHnflHUk+Qqbx7cAFugDUa2HFRtDe1QA==", "path": "system.servicemodel.primitives/6.0.0", "hashPath": "system.servicemodel.primitives.6.0.0.nupkg.sha512"}, "System.ServiceModel.Security/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-r/8fNXE/tzUgglw63px4jGc+Tdm3bh4nsMsAA2OvJYch9s4F243l549kBK9myqUVNp9FqnrTGn/1xNK1vh0LGQ==", "path": "system.servicemodel.security/6.0.0", "hashPath": "system.servicemodel.security.6.0.0.nupkg.sha512"}}}