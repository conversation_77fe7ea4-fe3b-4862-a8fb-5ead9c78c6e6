# DUO SAML Demo - React Frontend

This is a React frontend application that replicates the functionality and appearance of the original ASP.NET Core MVC SAML authentication demo.

## Features

- **Home Page**: Simple landing page with the DUO SAML Demo title
- **Claims Page**: Displays user claims after SAML authentication
- **Authentication**: Integrates with the existing SAML authentication flow
- **Responsive Design**: Uses Bootstrap for consistent styling with the original MVC app
- **Navigation**: Dynamic navigation bar that shows different options based on authentication status

## Project Structure

```
react-frontend/
├── public/
│   └── index.html          # HTML template
├── src/
│   ├── components/
│   │   ├── Layout.js       # Main layout component
│   │   ├── Navbar.js       # Navigation component
│   │   └── Footer.js       # Footer component
│   ├── pages/
│   │   ├── Home.js         # Home page component
│   │   └── Claims.js       # Claims page component
│   ├── services/
│   │   └── authService.js  # Authentication service
│   ├── context/
│   │   └── AuthContext.js  # React context for auth state
│   ├── App.js              # Main app component
│   ├── App.css             # Styles (replicating original site.css)
│   └── index.js            # React entry point
├── package.json            # Dependencies and scripts
└── README.md              # This file
```

## Getting Started

### Prerequisites

- Node.js (version 14 or higher)
- npm or yarn

### Installation

1. Navigate to the react-frontend directory:
   ```bash
   cd react-frontend
   ```

2. Install dependencies:
   ```bash
   npm install
   ```

3. Start the development server:
   ```bash
   npm start
   ```

4. Open [http://localhost:3000](http://localhost:3000) to view it in the browser.

## Integration with Backend

The React app is configured to work with the existing ASP.NET Core backend:

- **Proxy Configuration**: The `package.json` includes a proxy setting to forward API calls to `https://localhost:5001`
- **Authentication Flow**: Login redirects to `/Auth/Login` endpoint on the backend
- **SAML Claims**: Claims are fetched from the backend after authentication
- **Logout**: Logout calls the `/Auth/Logout` endpoint

## Available Scripts

- `npm start`: Runs the app in development mode
- `npm test`: Launches the test runner
- `npm run build`: Builds the app for production
- `npm run eject`: Ejects from Create React App (one-way operation)

## Styling

The application uses:
- **Bootstrap 5**: For responsive layout and components
- **Custom CSS**: Replicates the original `site.css` styles for consistency

## Authentication Flow

1. User clicks "Login" button
2. Redirected to SAML Identity Provider via `/Auth/Login`
3. After successful authentication, user is redirected back
4. React app checks authentication status and updates UI
5. Authenticated users can view claims on the Claims page

## Development Notes

- The authentication service includes mock data for development purposes
- In production, ensure proper CORS configuration between React and ASP.NET Core
- The app maintains the same visual appearance as the original MVC application
