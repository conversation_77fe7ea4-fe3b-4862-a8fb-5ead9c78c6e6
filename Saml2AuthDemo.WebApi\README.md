# Saml2AuthDemo Web API

A .NET Web API backend for SAML authentication with DUO Security integration.

## Overview

This Web API provides RESTful endpoints for SAML authentication and user management. It's designed to work with the React frontend but can be used with any client application.

## Features

- **SAML 2.0 Authentication** with DUO Security
- **RESTful API** endpoints
- **CORS Support** for cross-origin requests
- **Session Management** for authenticated users
- **Swagger Documentation** for API exploration
- **Claims Processing** and transformation

## API Endpoints

### Authentication API

#### GET /api/auth/status
Get the current authentication status and user claims.

**Response:**
```json
{
  "isAuthenticated": true,
  "user": {
    "name": "<EMAIL>"
  },
  "claims": [
    {
      "type": "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier",
      "value": "<EMAIL>"
    },
    {
      "type": "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name",
      "value": "<PERSON>"
    }
  ]
}
```

#### POST /api/auth/logout
Logout the current user.

**Response:**
```json
{
  "success": true,
  "message": "Logout successful"
}
```

### SAML Endpoints

#### GET /Auth/Login
Initiates SAML authentication by redirecting to the Identity Provider.

**Parameters:**
- `returnUrl` (optional): URL to redirect to after successful authentication

**Response:** HTTP 302 redirect to SAML IdP

#### POST /Auth/AssertionConsumerService
Handles the SAML response from the Identity Provider.

**Response:** HTTP 302 redirect to the return URL

#### POST /Auth/Logout
SAML logout endpoint that clears the session.

**Response:** HTTP 302 redirect to the frontend

## Configuration

### appsettings.json
```json
{
  "Saml2": {
    "IdPMetadata": "https://sso-dac3a302.sso.duosecurity.com/saml2/sp/DII1DN9S6K13NGQKFK6N/metadata",
    "Issuer": "http://localhost:5001",
    "SignatureAlgorithm": "http://www.w3.org/2001/04/xmldsig-more#rsa-sha256",
    "RevocationMode": "NoCheck"
  }
}
```

### CORS Configuration
The API is configured to allow requests from `http://localhost:3000` (React frontend) with credentials.

## Running the API

### Development
```bash
dotnet run --urls="http://localhost:5001"
```

### Production
```bash
dotnet publish -c Release
dotnet Saml2AuthDemo.WebApi.dll --urls="http://localhost:5001"
```

## Dependencies

- **ITfoxtec.Identity.Saml2** - SAML 2.0 implementation
- **ITfoxtec.Identity.Saml2.MvcCore** - ASP.NET Core integration
- **Swashbuckle.AspNetCore** - Swagger documentation

## Security Considerations

1. **HTTPS**: Use HTTPS in production
2. **CORS**: Configure CORS for your specific domains
3. **Session Security**: Sessions are secured with HTTP-only cookies
4. **Certificate Validation**: Configure proper certificate validation for production

## Swagger Documentation

When running in development mode, Swagger UI is available at:
- http://localhost:5001/swagger

This provides interactive documentation and testing capabilities for all API endpoints.

## Error Handling

The API returns appropriate HTTP status codes:
- **200 OK**: Successful requests
- **400 Bad Request**: Invalid requests or SAML errors
- **401 Unauthorized**: Authentication required
- **500 Internal Server Error**: Server errors

Error responses include descriptive messages:
```json
{
  "error": "Authentication failed",
  "details": "SAML Response status: Requester"
}
```
