using System.Security.Authentication;
using ITfoxtec.Identity.Saml2;
using ITfoxtec.Identity.Saml2.MvcCore;
using ITfoxtec.Identity.Saml2.Schemas;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Options;
using Saml2AuthDemo.Utils;

namespace Saml2AuthDemo.Controllers;

[AllowAnonymous]
[Route("Auth")]
public class AuthController(IOptions<Saml2Configuration> configAccessor) : Controller
{
    private const string RelayStateReturnUrl = "ReturnUrl";
    private readonly Saml2Configuration _config = configAccessor.Value;

    /*
     * This endpoint triggers the redirection to SAML SSO IDP
     */
    [Route("Login")]
    public IActionResult Login(string? returnUrl = null)
    {
        var binding = new Saml2RedirectBinding();
        binding.SetRelayStateQuery(new Dictionary<string, string> { { RelayStateReturnUrl, returnUrl ?? Url.Content("~/") } });

        return binding.Bind(new Saml2AuthnRequest(_config)).ToActionResult();
    }

    /*
     * This is called back by the SSO redirection as POST method with Assertions as payload and a session is maintained.
     */
    [Route("AssertionConsumerService")]
    public async Task<IActionResult> AssertionConsumerService()
    {
        var binding = new Saml2PostBinding();
        var saml2AuthnResponse = new Saml2AuthnResponse(_config);

        binding.ReadSamlResponse(Request.ToGenericHttpRequest(), saml2AuthnResponse);
        if (saml2AuthnResponse.Status != Saml2StatusCodes.Success)
        {
            throw new AuthenticationException($"SAML Response status: {saml2AuthnResponse.Status}");
        }
        binding.Unbind(Request.ToGenericHttpRequest(), saml2AuthnResponse);
        await saml2AuthnResponse.CreateSession(HttpContext, claimsTransform: ClaimsTransform.Transform);

        var relayStateQuery = binding.GetRelayStateQuery();
        var returnUrl = relayStateQuery.TryGetValue(RelayStateReturnUrl, out var value) ? value : Url.Content("~/");
        return Redirect(returnUrl);
    }

    [HttpPost("Logout")]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> Logout()
    {
        if (!User.Identity!.IsAuthenticated)
        {
            return Redirect(Url.Content("~/"));
        }

        var binding = new Saml2PostBinding();
        var saml2LogoutRequest = await new Saml2LogoutRequest(_config, User).DeleteSession(HttpContext);
        return Redirect("~/");
    }
}