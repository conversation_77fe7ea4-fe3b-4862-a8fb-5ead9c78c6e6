import React from 'react';
import { Link } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';

const Navbar = () => {
  const { isAuthenticated, user, login, logout } = useAuth();

  const handleLogin = (e) => {
    e.preventDefault();
    login();
  };

  const handleLogout = (e) => {
    e.preventDefault();
    logout();
  };

  return (
    <header>
      <nav className="navbar navbar-expand-sm navbar-toggleable-sm navbar-light bg-white border-bottom box-shadow mb-3">
        <div className="container">
          <Link className="navbar-brand" to="/">DUO SAML Demo</Link>
          <button 
            className="navbar-toggler" 
            type="button" 
            data-bs-toggle="collapse" 
            data-bs-target=".navbar-collapse" 
            aria-controls="navbarSupportedContent"
            aria-expanded="false" 
            aria-label="Toggle navigation"
          >
            <span className="navbar-toggler-icon"></span>
          </button>
          <div className="navbar-collapse collapse d-sm-inline-flex flex-sm-row-reverse">
            <ul className="navbar-nav flex-grow-1">
              <li className="nav-item">
                <Link className="nav-link text-dark" to="/">Home</Link>
              </li>
              {isAuthenticated ? (
                <>
                  <li className="nav-item">
                    <Link className="nav-link text-dark" to="/claims">SAML Claims</Link>
                  </li>
                  <li className="nav-item">
                    <span className="navbar-text">
                      Hello, {user?.name || 'User'}!
                    </span>
                  </li>
                  <li className="nav-item">
                    <button 
                      type="button" 
                      className="nav-link btn btn-link text-dark"
                      onClick={handleLogout}
                      style={{ border: 'none', background: 'none', padding: '0.5rem 1rem' }}
                    >
                      Logout
                    </button>
                  </li>
                </>
              ) : (
                <li className="nav-item">
                  <button 
                    type="button" 
                    className="nav-link btn btn-link text-dark"
                    onClick={handleLogin}
                    style={{ border: 'none', background: 'none', padding: '0.5rem 1rem' }}
                  >
                    Login
                  </button>
                </li>
              )}
            </ul>
          </div>
        </div>
      </nav>
    </header>
  );
};

export default Navbar;
