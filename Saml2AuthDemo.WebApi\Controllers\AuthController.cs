using System.Security.Authentication;
using ITfoxtec.Identity.Saml2;
using ITfoxtec.Identity.Saml2.MvcCore;
using ITfoxtec.Identity.Saml2.Schemas;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Options;
using Saml2AuthDemo.WebApi.Utils;

namespace Saml2AuthDemo.WebApi.Controllers;

[ApiController]
[Route("api/[controller]")]
[AllowAnonymous]
public class AuthController(IOptions<Saml2Configuration> configAccessor) : ControllerBase
{
    private const string RelayStateReturnUrl = "ReturnUrl";
    private readonly Saml2Configuration _config = configAccessor.Value;

    /// <summary>
    /// Get current authentication status and user claims
    /// </summary>
    [HttpGet("status")]
    public IActionResult GetAuthStatus()
    {
        var isAuthenticated = User.Identity?.IsAuthenticated ?? false;
        
        if (isAuthenticated)
        {
            var claims = User.Claims.Select(c => new { type = c.Type, value = c.Value }).ToList();
            var userName = User.Identity?.Name;
            
            return Ok(new
            {
                isAuthenticated = true,
                user = new { name = userName },
                claims = claims
            });
        }
        
        return Ok(new
        {
            isAuthenticated = false,
            user = (object?)null,
            claims = new List<object>()
        });
    }

    /// <summary>
    /// Initiate SAML login - redirects to Identity Provider
    /// </summary>
    [HttpGet("login")]
    public IActionResult Login([FromQuery] string? returnUrl = null)
    {
        var binding = new Saml2RedirectBinding();
        binding.SetRelayStateQuery(new Dictionary<string, string> 
        { 
            { RelayStateReturnUrl, returnUrl ?? "http://localhost:3000" } 
        });

        return binding.Bind(new Saml2AuthnRequest(_config)).ToActionResult();
    }

    /// <summary>
    /// SAML Assertion Consumer Service - handles SAML response from Identity Provider
    /// </summary>
    [HttpPost("acs")]
    public async Task<IActionResult> AssertionConsumerService()
    {
        try
        {
            var binding = new Saml2PostBinding();
            var saml2AuthnResponse = new Saml2AuthnResponse(_config);

            binding.ReadSamlResponse(Request.ToGenericHttpRequest(), saml2AuthnResponse);
            if (saml2AuthnResponse.Status != Saml2StatusCodes.Success)
            {
                return BadRequest(new { error = $"SAML Response status: {saml2AuthnResponse.Status}" });
            }
            
            binding.Unbind(Request.ToGenericHttpRequest(), saml2AuthnResponse);
            await saml2AuthnResponse.CreateSession(HttpContext, claimsTransform: ClaimsTransform.Transform);

            var relayStateQuery = binding.GetRelayStateQuery();
            var returnUrl = relayStateQuery.TryGetValue(RelayStateReturnUrl, out var value) ? value : "http://localhost:3000";
            
            // For API, we'll return success and let the frontend handle the redirect
            return Ok(new { success = true, returnUrl = returnUrl });
        }
        catch (AuthenticationException ex)
        {
            return BadRequest(new { error = ex.Message });
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { error = "Authentication failed", details = ex.Message });
        }
    }

    /// <summary>
    /// Logout current user
    /// </summary>
    [HttpPost("logout")]
    public async Task<IActionResult> Logout()
    {
        try
        {
            if (!User.Identity!.IsAuthenticated)
            {
                return Ok(new { success = true, message = "User was not authenticated" });
            }

            var binding = new Saml2PostBinding();
            var saml2LogoutRequest = await new Saml2LogoutRequest(_config, User).DeleteSession(HttpContext);
            
            return Ok(new { success = true, message = "Logout successful" });
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { error = "Logout failed", details = ex.Message });
        }
    }
}
