# DUO SAML Demo - Full Stack Application

A complete SAML authentication demo application with a .NET Web API backend and React frontend.

## Architecture

```
┌─────────────────┐    HTTP/API     ┌──────────────────┐    SAML     ┌─────────────────┐
│  React Frontend │ ◄──────────────► │ .NET Web API     │ ◄──────────► │ SAML Identity   │
│  (Port 3000)    │                 │ Backend          │             │ Provider (DUO)  │
│                 │                 │ (Port 5001)      │             │                 │
└─────────────────┘                 └──────────────────┘             └─────────────────┘
```

## Project Structure

```
├── Saml2AuthDemo.WebApi/          # .NET Web API Backend
│   ├── Controllers/
│   │   ├── AuthController.cs      # API endpoints for authentication
│   │   └── SamlController.cs      # SAML-specific endpoints
│   ├── Utils/
│   │   └── ClaimsTransform.cs     # SAML claims transformation
│   ├── Certs/
│   │   └── duo.crt               # SAML certificate
│   ├── Program.cs                # Application startup
│   └── appsettings.json          # Configuration
├── react-frontend/               # React Frontend Application
│   ├── src/
│   │   ├── components/           # React components
│   │   ├── pages/               # Page components
│   │   ├── services/            # API services
│   │   └── context/             # React context
│   └── package.json             # Frontend dependencies
├── Saml2AuthDemo/               # Original MVC Application (legacy)
├── start-dev.sh                 # Unix/Linux/macOS startup script
├── start-dev.bat               # Windows batch startup script
├── start-dev.ps1               # PowerShell startup script
└── README.md                   # This file
```

## Features

### Backend (.NET Web API)
- **SAML 2.0 Authentication**: Integration with DUO Security SAML Identity Provider
- **RESTful API**: Clean API endpoints for authentication status and user claims
- **CORS Support**: Configured for React frontend communication
- **Session Management**: Secure session handling for SAML authentication
- **Swagger Documentation**: Auto-generated API documentation

### Frontend (React)
- **Modern React**: Built with React 18 and functional components
- **Authentication Context**: Global authentication state management
- **Responsive Design**: Bootstrap-based UI matching the original MVC design
- **Dynamic Navigation**: Context-aware navigation based on authentication status
- **Claims Display**: User-friendly display of SAML claims
- **Demo Mode**: Mock authentication for development and testing

## API Endpoints

### Authentication API (`/api/auth`)
- `GET /api/auth/status` - Get current authentication status and user claims
- `POST /api/auth/logout` - Logout current user

### SAML Endpoints (`/Auth`)
- `GET /Auth/Login` - Initiate SAML authentication (redirects to IdP)
- `POST /Auth/AssertionConsumerService` - Handle SAML response from IdP
- `POST /Auth/Logout` - SAML logout endpoint

## Quick Start

### Prerequisites
- **.NET 9.0 SDK** or later
- **Node.js 14+** and **npm**
- **Git** (for cloning)

### Option 1: Automated Setup (Recommended)

#### Windows (PowerShell)
```powershell
.\start-dev.ps1
```

#### Windows (Command Prompt)
```cmd
start-dev.bat
```

#### Unix/Linux/macOS
```bash
chmod +x start-dev.sh
./start-dev.sh
```

### Option 2: Manual Setup

#### 1. Start the Backend API
```bash
cd Saml2AuthDemo.WebApi
dotnet run --urls="http://localhost:5001"
```

#### 2. Start the React Frontend
```bash
cd react-frontend
npm install
npm start
```

## Access Points

- **React Frontend**: http://localhost:3000
- **Backend API**: http://localhost:5001
- **API Documentation**: http://localhost:5001/swagger

## Configuration

### Backend Configuration (`Saml2AuthDemo.WebApi/appsettings.json`)
```json
{
  "Saml2": {
    "IdPMetadata": "https://sso-dac3a302.sso.duosecurity.com/saml2/sp/DII1DN9S6K13NGQKFK6N/metadata",
    "Issuer": "http://localhost:5001",
    "SignatureAlgorithm": "http://www.w3.org/2001/04/xmldsig-more#rsa-sha256",
    "RevocationMode": "NoCheck"
  }
}
```

### Frontend Configuration (`react-frontend/.env`)
```
REACT_APP_API_URL=http://localhost:5001
```

## Development

### Backend Development
- Built with **.NET 9.0** and **ASP.NET Core**
- Uses **ITfoxtec.Identity.Saml2** for SAML implementation
- **Swagger UI** available at `/swagger` for API testing
- **Hot reload** enabled for development

### Frontend Development
- Built with **Create React App**
- **Hot reload** enabled for development
- **Bootstrap 5** for styling
- **React Router** for navigation
- **Context API** for state management

## Authentication Flow

1. **User clicks "Login"** in React frontend
2. **Frontend redirects** to `/Auth/Login` on backend
3. **Backend redirects** to SAML Identity Provider (DUO)
4. **User authenticates** with DUO
5. **DUO posts SAML response** to `/Auth/AssertionConsumerService`
6. **Backend processes SAML response** and creates session
7. **Backend redirects** back to React frontend
8. **Frontend checks authentication status** via `/api/auth/status`
9. **User can view claims** on the Claims page

## Troubleshooting

### Common Issues

1. **CORS Errors**: Ensure backend is running on port 5001 and frontend on port 3000
2. **Certificate Issues**: Verify `duo.crt` is in the `Certs` folder
3. **Port Conflicts**: Check if ports 3000 or 5001 are already in use
4. **Dependencies**: Run `npm install` in react-frontend if packages are missing

### Logs
- **Backend logs**: Console output from the .NET application
- **Frontend logs**: Browser console and terminal output

## Production Deployment

### Backend
- Configure HTTPS certificates
- Update SAML configuration for production URLs
- Set up proper logging and monitoring
- Configure production database if needed

### Frontend
- Build production bundle: `npm run build`
- Serve static files through web server (nginx, IIS, etc.)
- Update API URL environment variables

## License

This project is for demonstration purposes. Please ensure compliance with your organization's security policies when using SAML authentication.
