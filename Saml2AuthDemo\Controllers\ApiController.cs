using Microsoft.AspNetCore.Mvc;
using System.Security.Claims;

namespace Saml2AuthDemo.Controllers;

[ApiController]
[Route("api")]
public class ApiController : ControllerBase
{
    [HttpGet("auth/status")]
    public IActionResult GetAuthStatus()
    {
        var isAuthenticated = User.Identity?.IsAuthenticated ?? false;
        
        if (isAuthenticated)
        {
            var claims = User.Claims.Select(c => new { type = c.Type, value = c.Value }).ToList();
            var userName = User.Identity?.Name;
            
            return Ok(new
            {
                isAuthenticated = true,
                user = new { name = userName },
                claims = claims
            });
        }
        
        return Ok(new
        {
            isAuthenticated = false,
            user = (object?)null,
            claims = new List<object>()
        });
    }
}
